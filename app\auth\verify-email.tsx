import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { supabase, isSupabaseConfigured } from '@/lib/supabase';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  ArrowLeft,
  Mail,
  Shield,
  RefreshCw,
  Info,
  TriangleAlert as AlertTriangle,
  CheckCircle,
} from 'lucide-react-native';

export default function VerifyEmailScreen() {
  const { email } = useLocalSearchParams<{ email: string }>();
  const [otp, setOtp] = useState('');
  const [loading, setLoading] = useState(false);
  const [resending, setResending] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [attemptsRemaining, setAttemptsRemaining] = useState(5);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isVerified, setIsVerified] = useState(false);
  const [isBlocked, setIsBlocked] = useState(false);
  const [blockCountdown, setBlockCountdown] = useState(0);
  const mounted = useRef(true);

  const clearMessages = () => {
    setError('');
    setSuccess('');
  };

  useEffect(() => {
    mounted.current = true;

    // Start countdown for resend button
    if (countdown > 0) {
      const timer = setTimeout(() => {
        if (mounted.current) {
          setCountdown(countdown - 1);
        }
      }, 1000);
      return () => clearTimeout(timer);
    }

    return () => {
      mounted.current = false;
    };
  }, [countdown]);

  useEffect(() => {
    // Initial countdown when component mounts
    setCountdown(60);
  }, []);

  const handleVerifyOTP = async () => {
    clearMessages();

    if (!email || !otp) {
      setError('Please enter the verification code');
      return;
    }

    if (otp.length !== 6) {
      setError('Please enter a valid 6-digit code');
      return;
    }

    if (!isSupabaseConfigured()) {
      // In demo mode, simulate successful verification
      setIsVerified(true);
      setSuccess(
        'Email verification simulated successfully! You can now access the app.'
      );
      setTimeout(() => {
        router.replace('/auth/login');
      }, 2000);
      return;
    }

    setLoading(true);
    try {
      const { data, error } = await supabase.rpc('verify_email_otp', {
        user_email: email,
        otp_code: otp,
      });

      if (error) throw error;

      if (data.success) {
        setIsVerified(true);
        setSuccess('Email verified successfully! You can now access the app.');
        setTimeout(() => {
          router.replace('/auth/login');
        }, 2000);
      } else {
        const errorMessage = data.error || 'Verification failed';
        const remaining = data.attempts_remaining;
        const retryAfterSeconds = data.retry_after_seconds;

        // Handle blocking scenario
        if (retryAfterSeconds && retryAfterSeconds > 0) {
          setIsBlocked(true);
          setBlockCountdown(retryAfterSeconds);
          setError(
            `${errorMessage}\n\nYou can try again in ${Math.ceil(
              retryAfterSeconds / 60
            )} minutes.`
          );

          // Start countdown timer
          const timer = setInterval(() => {
            setBlockCountdown((prev) => {
              if (prev <= 1) {
                clearInterval(timer);
                setIsBlocked(false);
                setError('');
                return 0;
              }
              return prev - 1;
            });
          }, 1000);
        } else if (remaining !== undefined) {
          setAttemptsRemaining(remaining);
          setError(errorMessage);
        } else {
          setError(errorMessage);
        }
      }
    } catch (error: any) {
      setError(error.message || 'Verification failed');
    } finally {
      if (mounted.current) {
        setLoading(false);
      }
    }
  };

  const handleResendCode = async () => {
    if (!email) return;
    clearMessages();

    if (!isSupabaseConfigured()) {
      setSuccess(
        'In demo mode, use any 6-digit code (e.g., 123456) to verify your email.'
      );
      setCountdown(60);
      setOtp('');
      setAttemptsRemaining(5);
      return;
    }

    setResending(true);
    try {
      const { data, error } = await supabase.rpc('create_email_verification', {
        user_email: email,
      });

      if (error) throw error;

      if (data.success) {
        setSuccess(
          data.email_sent
            ? 'A new verification code has been sent to your email'
            : 'Verification code generated (email service not configured)'
        );
        setCountdown(60);
        setOtp('');
        setAttemptsRemaining(5);
        setIsBlocked(false);
        setBlockCountdown(0);
      } else {
        const retryAfterSeconds = data.retry_after_seconds;
        if (retryAfterSeconds && retryAfterSeconds > 0) {
          setError(
            `${
              data.error || 'Failed to send verification code'
            }\n\nYou can try again in ${Math.ceil(
              retryAfterSeconds / 60
            )} minutes.`
          );
        } else {
          setError(data.error || 'Failed to send verification code');
        }
      }
    } catch (error: any) {
      setError(error.message || 'Failed to send verification code');
    } finally {
      if (mounted.current) {
        setResending(false);
      }
    }
  };

  const formatOTP = (value: string) => {
    // Only allow numbers and limit to 6 digits
    const numbers = value.replace(/[^0-9]/g, '').slice(0, 6);
    return numbers;
  };

  const isConfigured = isSupabaseConfigured();

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <ArrowLeft size={24} color="#1F2937" />
        </TouchableOpacity>
        <Text style={styles.title}>Verify Email</Text>
        <View style={{ width: 24 }} />
      </View>

      <View style={styles.content}>
        {!isConfigured && (
          <View style={styles.demoNotice}>
            <Info size={20} color="#F59E0B" />
            <View style={styles.demoNoticeContent}>
              <Text style={styles.demoNoticeTitle}>Demo Mode</Text>
              <Text style={styles.demoNoticeText}>
                Email verification is simulated. Use any 6-digit code (e.g.,
                123456) to proceed.
              </Text>
            </View>
          </View>
        )}

        <View style={styles.iconContainer}>
          <Shield size={64} color="#3B82F6" />
        </View>

        <Text style={styles.heading}>Check your email</Text>
        <Text style={styles.description}>
          We've sent a 6-digit verification code to:
        </Text>
        <Text style={styles.email}>{email}</Text>

        {error && (
          <View style={styles.errorContainer}>
            <AlertTriangle size={20} color="#DC2626" />
            <View style={styles.errorContent}>
              <Text style={styles.errorText}>{error}</Text>
            </View>
          </View>
        )}

        {success && (
          <View style={styles.successContainer}>
            <CheckCircle size={20} color="#10B981" />
            <View style={styles.successContent}>
              <Text style={styles.successText}>{success}</Text>
            </View>
          </View>
        )}

        <View style={styles.otpContainer}>
          <Mail size={20} color="#6B7280" />
          <TextInput
            style={[styles.otpInput, error && styles.inputError]}
            placeholder="Enter 6-digit code"
            value={otp}
            onChangeText={(text) => {
              setOtp(formatOTP(text));
              clearMessages();
            }}
            keyboardType="numeric"
            maxLength={6}
            autoCapitalize="none"
            autoComplete="one-time-code"
            textContentType="oneTimeCode"
          />
        </View>

        {attemptsRemaining < 5 && (
          <View style={styles.attemptsWarning}>
            <Text style={styles.attemptsText}>
              Attempts remaining: {attemptsRemaining}
            </Text>
          </View>
        )}

        <TouchableOpacity
          style={[
            styles.verifyButton,
            (!otp || loading || isBlocked) && styles.buttonDisabled,
          ]}
          onPress={handleVerifyOTP}
          disabled={!otp || loading || isBlocked}
        >
          <Text style={styles.verifyButtonText}>
            {loading ? 'Verifying...' : isBlocked ? 'Blocked' : 'Verify Email'}
          </Text>
        </TouchableOpacity>

        <View style={styles.resendContainer}>
          <Text style={styles.resendText}>Didn't receive the code? </Text>
          <TouchableOpacity
            onPress={handleResendCode}
            disabled={countdown > 0 || resending || isBlocked}
            style={styles.resendButton}
          >
            <RefreshCw
              size={16}
              color={
                countdown > 0 || resending || isBlocked ? '#9CA3AF' : '#3B82F6'
              }
            />
            <Text
              style={[
                styles.resendButtonText,
                (countdown > 0 || resending || isBlocked) &&
                  styles.resendButtonDisabled,
              ]}
            >
              {resending
                ? 'Sending...'
                : isBlocked
                ? `Blocked (${Math.ceil(blockCountdown / 60)}m)`
                : countdown > 0
                ? `Resend in ${countdown}s`
                : 'Resend Code'}
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.helpContainer}>
          <Text style={styles.helpText}>
            {isConfigured
              ? '• Check your spam/junk folder\n• Make sure you entered the correct email\n• Code expires in 10 minutes'
              : '• Demo mode: Use any 6-digit code\n• Example: 123456\n• Real email sending requires Supabase configuration'}
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    paddingVertical: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1F2937',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 32,
  },
  demoNotice: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#FEF3C7',
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
    gap: 12,
  },
  demoNoticeContent: {
    flex: 1,
  },
  demoNoticeTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#92400E',
    marginBottom: 4,
  },
  demoNoticeText: {
    fontSize: 14,
    color: '#92400E',
    lineHeight: 20,
  },
  iconContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  heading: {
    fontSize: 28,
    fontWeight: '700',
    color: '#1F2937',
    textAlign: 'center',
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 8,
  },
  email: {
    fontSize: 16,
    fontWeight: '600',
    color: '#3B82F6',
    textAlign: 'center',
    marginBottom: 32,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#FEF2F2',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    gap: 12,
  },
  errorContent: {
    flex: 1,
  },
  errorText: {
    fontSize: 14,
    color: '#DC2626',
    lineHeight: 20,
  },
  successContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#F0FDF4',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    gap: 12,
  },
  successContent: {
    flex: 1,
  },
  successText: {
    fontSize: 14,
    color: '#059669',
    lineHeight: 20,
  },
  otpContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderRadius: 12,
    marginBottom: 16,
    gap: 12,
  },
  otpInput: {
    flex: 1,
    fontSize: 18,
    color: '#1F2937',
    fontWeight: '600',
    letterSpacing: 2,
    textAlign: 'center',
  },
  inputError: {
    borderColor: '#DC2626',
    borderWidth: 1,
  },
  attemptsWarning: {
    backgroundColor: '#FEF2F2',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  attemptsText: {
    fontSize: 14,
    color: '#DC2626',
    textAlign: 'center',
    fontWeight: '500',
  },
  verifyButton: {
    backgroundColor: '#3B82F6',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 24,
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  verifyButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  resendContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 32,
  },
  resendText: {
    fontSize: 14,
    color: '#6B7280',
  },
  resendButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  resendButtonText: {
    fontSize: 14,
    color: '#3B82F6',
    fontWeight: '500',
  },
  resendButtonDisabled: {
    color: '#9CA3AF',
  },
  helpContainer: {
    backgroundColor: '#F0F9FF',
    padding: 16,
    borderRadius: 12,
  },
  helpText: {
    fontSize: 14,
    color: '#1E40AF',
    lineHeight: 20,
  },
});
