import { StateCreator } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AssessmentStore } from './types';
import { STORAGE_KEYS } from './constants';

// Persistence configuration
export const persistConfig = {
  name: STORAGE_KEYS.ASSESSMENT_STATE,
  storage: createJSONStorage(() => AsyncStorage),
  partialize: (state: AssessmentStore) => ({
    // Only persist essential state for recovery
    isRetake: state.isRetake,
    useAI: state.useAI,
    isDemo: state.isDemo,
    config: state.config,
    // Don't persist sensitive data or temporary state
  }),
  version: 1,
  migrate: (persistedState: any, version: number) => {
    // Handle migration between versions if needed
    if (version === 0) {
      // Migrate from version 0 to 1
      return {
        ...persistedState,
        config: persistedState.config || {},
      };
    }
    return persistedState;
  },
};

// Middleware for automatic progress saving
export const createProgressMiddleware = <T extends AssessmentStore>(
  config: StateCreator<T>
): StateCreator<T> => (set, get, api) => {
  const originalSet = set;
  
  // Override set to add progress tracking
  const enhancedSet = (partial: any, replace?: boolean) => {
    const prevState = get();
    originalSet(partial, replace);
    const newState = get();
    
    // Track progress changes
    if (prevState.responses.length !== newState.responses.length) {
      console.log(`📊 Progress: ${newState.responses.length}/${newState.questions.length} questions completed`);
      
      // Auto-save progress periodically
      if (newState.responses.length % 2 === 0) {
        console.log('💾 Auto-saving progress...');
        // Progress is automatically saved by Zustand persist middleware
      }
    }
    
    // Track phase transitions
    if (prevState.phase !== newState.phase) {
      console.log(`🔄 Phase transition: ${prevState.phase} → ${newState.phase}`);
    }
    
    // Track error state changes
    if (!prevState.error && newState.error) {
      console.error('❌ Error occurred:', newState.error);
    } else if (prevState.error && !newState.error) {
      console.log('✅ Error cleared');
    }
  };
  
  return config(enhancedSet, get, api);
};

// Middleware for performance monitoring
export const createPerformanceMiddleware = <T extends AssessmentStore>(
  config: StateCreator<T>
): StateCreator<T> => (set, get, api) => {
  const startTime = Date.now();
  let actionCount = 0;
  let lastActionTime = startTime;
  
  const originalSet = set;
  
  const enhancedSet = (partial: any, replace?: boolean) => {
    const actionStartTime = Date.now();
    actionCount++;
    
    originalSet(partial, replace);
    
    const actionEndTime = Date.now();
    const actionDuration = actionEndTime - actionStartTime;
    const timeSinceLastAction = actionStartTime - lastActionTime;
    
    if (__DEV__ && actionDuration > 100) {
      console.warn(`⚠️ Slow state update detected: ${actionDuration}ms`);
    }
    
    if (__DEV__ && actionCount % 10 === 0) {
      const totalTime = actionEndTime - startTime;
      const avgActionTime = totalTime / actionCount;
      console.log(`📈 Performance stats: ${actionCount} actions, avg ${avgActionTime.toFixed(2)}ms per action`);
    }
    
    lastActionTime = actionEndTime;
  };
  
  return config(enhancedSet, get, api);
};

// Middleware for error tracking and recovery
export const createErrorTrackingMiddleware = <T extends AssessmentStore>(
  config: StateCreator<T>
): StateCreator<T> => (set, get, api) => {
  const errorHistory: Array<{
    error: any;
    timestamp: string;
    state: Partial<T>;
  }> = [];
  
  const originalSet = set;
  
  const enhancedSet = (partial: any, replace?: boolean) => {
    const prevState = get();
    originalSet(partial, replace);
    const newState = get();
    
    // Track new errors
    if (!prevState.error && newState.error) {
      const errorRecord = {
        error: newState.error,
        timestamp: new Date().toISOString(),
        state: {
          phase: newState.phase,
          currentQuestionIndex: newState.currentQuestionIndex,
          responses: newState.responses,
          isRetake: newState.isRetake,
          useAI: newState.useAI,
        },
      };
      
      errorHistory.push(errorRecord);
      
      // Keep only last 10 errors
      if (errorHistory.length > 10) {
        errorHistory.shift();
      }
      
      // Log error for debugging
      console.error('🚨 Error tracked:', errorRecord);
      
      // In production, you might want to send this to an error tracking service
      if (!__DEV__) {
        // sendToErrorTrackingService(errorRecord);
      }
    }
  };
  
  // Add error history to the store
  const enhancedConfig = config(enhancedSet, get, api);
  
  return {
    ...enhancedConfig,
    getErrorHistory: () => errorHistory,
    clearErrorHistory: () => {
      errorHistory.length = 0;
    },
  } as T;
};

// Middleware for action logging
export const createLoggingMiddleware = <T extends AssessmentStore>(
  config: StateCreator<T>
): StateCreator<T> => (set, get, api) => {
  if (!__DEV__) {
    return config(set, get, api);
  }
  
  const originalSet = set;
  
  const enhancedSet = (partial: any, replace?: boolean) => {
    const prevState = get();
    
    // Log state changes in development
    console.group('🔄 State Update');
    console.log('Previous state:', {
      phase: prevState.phase,
      currentQuestionIndex: prevState.currentQuestionIndex,
      responses: prevState.responses.length,
      isLoading: prevState.isLoading,
      error: prevState.error?.type,
    });
    
    originalSet(partial, replace);
    
    const newState = get();
    console.log('New state:', {
      phase: newState.phase,
      currentQuestionIndex: newState.currentQuestionIndex,
      responses: newState.responses.length,
      isLoading: newState.isLoading,
      error: newState.error?.type,
    });
    
    console.log('Changes:', partial);
    console.groupEnd();
  };
  
  return config(enhancedSet, get, api);
};

// Combine all middleware
export const withMiddleware = <T extends AssessmentStore>(
  config: StateCreator<T>
): StateCreator<T> => {
  return createLoggingMiddleware(
    createErrorTrackingMiddleware(
      createPerformanceMiddleware(
        createProgressMiddleware(config)
      )
    )
  );
};

// Export the persist middleware with configuration
export const withPersistence = <T extends AssessmentStore>(
  config: StateCreator<T>
): StateCreator<T> => {
  return persist(config, persistConfig) as StateCreator<T>;
};

// Utility functions for middleware

// Clear all persisted data
export const clearPersistedData = async () => {
  try {
    await AsyncStorage.removeItem(STORAGE_KEYS.ASSESSMENT_STATE);
    console.log('🗑️ Persisted assessment data cleared');
  } catch (error) {
    console.error('❌ Failed to clear persisted data:', error);
  }
};

// Get persisted data size
export const getPersistedDataSize = async (): Promise<number> => {
  try {
    const data = await AsyncStorage.getItem(STORAGE_KEYS.ASSESSMENT_STATE);
    return data ? new Blob([data]).size : 0;
  } catch (error) {
    console.error('❌ Failed to get persisted data size:', error);
    return 0;
  }
};

// Validate persisted data
export const validatePersistedData = async (): Promise<boolean> => {
  try {
    const data = await AsyncStorage.getItem(STORAGE_KEYS.ASSESSMENT_STATE);
    if (!data) return true;
    
    const parsed = JSON.parse(data);
    
    // Basic validation
    if (typeof parsed !== 'object') return false;
    if (parsed.state && typeof parsed.state !== 'object') return false;
    
    return true;
  } catch (error) {
    console.error('❌ Persisted data validation failed:', error);
    return false;
  }
};
