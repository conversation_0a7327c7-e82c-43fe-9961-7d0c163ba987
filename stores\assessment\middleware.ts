import { StateCreator } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AssessmentStore } from './types';
import { STORAGE_KEYS } from './constants';

// Persistence configuration
export const persistConfig = {
  name: STORAGE_KEYS.ASSESSMENT_STATE,
  storage: createJSONStorage(() => AsyncStorage),
  partialize: (state: AssessmentStore) => ({
    // Only persist essential state for recovery
    isRetake: state.isRetake,
    useAI: state.useAI,
    isDemo: state.isDemo,
    config: state.config,
    // Don't persist sensitive data or temporary state
  }),
  version: 1,
  migrate: (persistedState: any, version: number) => {
    // Handle migration between versions if needed
    if (version === 0) {
      // Migrate from version 0 to 1
      return {
        ...persistedState,
        config: persistedState.config || {},
      };
    }
    return persistedState;
  },
};

// Simplified middleware - just pass through for now
export const createProgressMiddleware = <T extends AssessmentStore>(
  config: StateCreator<T>
): StateCreator<T> => config;

// Simplified performance middleware
export const createPerformanceMiddleware = <T extends AssessmentStore>(
  config: StateCreator<T>
): StateCreator<T> => config;

// Simplified error tracking middleware
export const createErrorTrackingMiddleware = <T extends AssessmentStore>(
  config: StateCreator<T>
): StateCreator<T> => config;

// Simplified logging middleware
export const createLoggingMiddleware = <T extends AssessmentStore>(
  config: StateCreator<T>
): StateCreator<T> => config;

// Combine all middleware
export const withMiddleware = <T extends AssessmentStore>(
  config: StateCreator<T>
): StateCreator<T> => {
  return createLoggingMiddleware(
    createErrorTrackingMiddleware(
      createPerformanceMiddleware(createProgressMiddleware(config))
    )
  );
};

// Export the persist middleware with configuration
export const withPersistence = (config: StateCreator<AssessmentStore>) => {
  return persist(config, persistConfig);
};

// Utility functions for middleware

// Clear all persisted data
export const clearPersistedData = async () => {
  try {
    await AsyncStorage.removeItem(STORAGE_KEYS.ASSESSMENT_STATE);
    console.log('🗑️ Persisted assessment data cleared');
  } catch (error) {
    console.error('❌ Failed to clear persisted data:', error);
  }
};

// Get persisted data size
export const getPersistedDataSize = async (): Promise<number> => {
  try {
    const data = await AsyncStorage.getItem(STORAGE_KEYS.ASSESSMENT_STATE);
    return data ? new Blob([data]).size : 0;
  } catch (error) {
    console.error('❌ Failed to get persisted data size:', error);
    return 0;
  }
};

// Validate persisted data
export const validatePersistedData = async (): Promise<boolean> => {
  try {
    const data = await AsyncStorage.getItem(STORAGE_KEYS.ASSESSMENT_STATE);
    if (!data) return true;

    const parsed = JSON.parse(data);

    // Basic validation
    if (typeof parsed !== 'object') return false;
    if (parsed.state && typeof parsed.state !== 'object') return false;

    return true;
  } catch (error) {
    console.error('❌ Persisted data validation failed:', error);
    return false;
  }
};
