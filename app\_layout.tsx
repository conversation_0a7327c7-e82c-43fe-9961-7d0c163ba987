import { useEffect } from 'react';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useFrameworkReady } from '@/hooks/useFrameworkReady';
import { initializeAuth, useAuthStore } from '@/stores/authStore';
import { initializeProfile, useProfileStore } from '@/stores/profileStore';
import { initializeAssessment } from '@/stores/assessmentStore';
import { initializeMoodState } from '@/stores/moodStore';
import { initializeCircle } from '@/stores/circleStore';

export default function RootLayout() {
  useFrameworkReady();

  // Initialize app state on load with proper synchronization
  useEffect(() => {
    const initializeApp = async () => {
      await initializeAuth();

      // Wait for auth to be initialized and profile to load
      const user = useAuthStore.getState().user;
      if (user) {
        await initializeProfile();

        // Initialize user-specific stores only when authenticated
        await initializeMoodState();
        await initializeCircle();

        // Check if assessment is already completed
        const profile = useProfileStore.getState().profile;
        if (profile?.has_completed_assessment) {
          // Skip assessment initialization if already completed
          return;
        }
      }

      // Only initialize assessment if needed (can work without auth for demo)
      await initializeAssessment();
    };

    initializeApp();
  }, []);

  return (
    <>
      <Stack screenOptions={{ headerShown: false }}>
        <Stack.Screen name="index" />
        <Stack.Screen name="auth" />
        <Stack.Screen name="assessment" />
        <Stack.Screen name="result" />
        <Stack.Screen name="(tabs)" />
        <Stack.Screen name="+not-found" />
      </Stack>
      <StatusBar style="auto" />
    </>
  );
}
