// Assessment-related constants and configurations

export const TEMPERAMENT_MAPPING = [
  'choleric',
  'sanguine', 
  'melancholic',
  'phlegmatic',
] as const;

export const TEMPERAMENT_COLORS = {
  choleric: '#EF4444',    // Red - Bold, energetic
  sanguine: '#F59E0B',    // Orange - Warm, social
  melancholic: '#3B82F6', // Blue - Thoughtful, analytical
  phlegmatic: '#10B981',  // Green - Calm, peaceful
} as const;

export const ASSESSMENT_PHASES = {
  INITIAL: 'initial',
  CONFIRMATION: 'confirmation', 
  COMPARISON: 'comparison',
  COMPLETE: 'complete',
} as const;

export const ASSESSMENT_CATEGORIES = {
  INITIAL: 'initial',
  CONFIRMATION: 'confirmation',
  COMPARISON: 'comparison', 
  TIEBREAKER: 'tiebreaker',
} as const;

export const LOADING_STATES = {
  IDLE: 'idle',
  LOADING: 'loading',
  GENERATING: 'generating',
  SUBMITTING: 'submitting',
} as const;

export const ERROR_TYPES = {
  NETWORK: 'network',
  VALIDATION: 'validation',
  AI_GENERATION: 'ai_generation',
  SUBMISSION: 'submission',
  AUTHENTICATION: 'authentication',
} as const;

// Assessment configuration
export const ASSESSMENT_CONFIG = {
  QUESTIONS_PER_PHASE: {
    INITIAL: 4,
    CONFIRMATION: 2,
    COMPARISON: 3,
  },
  MAX_RETRIES: 3,
  RETRY_DELAY_MS: 1000,
  AI_TIMEOUT_MS: 30000,
  PROGRESS_SAVE_INTERVAL_MS: 5000,
} as const;

// Question generation settings
export const QUESTION_GENERATION = {
  TEMPERATURE: 0.8,
  MAX_TOKENS: 6000,
  TOP_P: 1,
  MODEL: 'google/gemini-2.5-flash-preview-05-20',
} as const;

// Temperament compatibility matrix
export const TEMPERAMENT_COMPATIBILITY = {
  choleric: ['sanguine', 'melancholic'],
  sanguine: ['choleric', 'phlegmatic'],
  melancholic: ['choleric', 'phlegmatic'],
  phlegmatic: ['sanguine', 'melancholic'],
} as const;

// Scoring weights for different phases
export const PHASE_WEIGHTS = {
  INITIAL: 1.0,
  CONFIRMATION: 1.5,
  COMPARISON: 2.0,
} as const;

// UI Constants
export const UI_CONSTANTS = {
  ANIMATION_DURATION: 300,
  DEBOUNCE_DELAY: 500,
  TOAST_DURATION: 3000,
  PROGRESS_BAR_HEIGHT: 4,
} as const;

// Error messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network connection failed. Please check your internet connection.',
  AI_GENERATION_FAILED: 'Failed to generate AI questions. Using standard questions instead.',
  SUBMISSION_FAILED: 'Failed to submit assessment. Please try again.',
  AUTHENTICATION_REQUIRED: 'Please log in to continue with the assessment.',
  INVALID_RESPONSE: 'Invalid response received. Please try again.',
  TIMEOUT: 'Request timed out. Please try again.',
} as const;

// Success messages
export const SUCCESS_MESSAGES = {
  ASSESSMENT_COMPLETED: 'Assessment completed successfully!',
  PROFILE_UPDATED: 'Your personality profile has been updated.',
  QUESTIONS_GENERATED: 'Fresh questions generated successfully.',
} as const;

// Type exports for better type safety
export type TemperamentType = typeof TEMPERAMENT_MAPPING[number];
export type AssessmentPhase = typeof ASSESSMENT_PHASES[keyof typeof ASSESSMENT_PHASES];
export type AssessmentCategory = typeof ASSESSMENT_CATEGORIES[keyof typeof ASSESSMENT_CATEGORIES];
export type LoadingState = typeof LOADING_STATES[keyof typeof LOADING_STATES];
export type ErrorType = typeof ERROR_TYPES[keyof typeof ERROR_TYPES];
