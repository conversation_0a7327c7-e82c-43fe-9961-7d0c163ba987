import { TemperamentType } from '../../types/personality';

// Enhanced error types for better error handling
export interface AssessmentError {
  type:
    | 'network'
    | 'validation'
    | 'ai_generation'
    | 'submission'
    | 'authentication';
  message: string;
  retryable: boolean;
  context?: Record<string, any>;
  timestamp?: string;
}

// Define the structure for a single assessment question
export interface AssessmentQuestion {
  questionNumber: number;
  category: 'initial' | 'confirmation' | 'comparison' | 'tiebreaker';
  question: string;
  answers: string[];
  temperamentOrder: number[]; // Maps answer positions to temperament indices
}

// Define the structure for the entire assessment
export interface AssessmentQuestions {
  instructions: string;
  questions: AssessmentQuestion[];
}

// Define the result structure
export interface AssessmentResult {
  primaryTemperament: TemperamentType;
  secondaryTemperament: TemperamentType;
  primaryPercentage: number;
  secondaryPercentage: number;
  temperamentScores: {
    choleric: number;
    sanguine: number;
    melancholic: number;
    phlegmatic: number;
  };
  completedAt: string;
}

// Temperament scores interface
export interface TemperamentScores {
  choleric: number;
  sanguine: number;
  melancholic: number;
  phlegmatic: number;
}

// Assessment phases
export type AssessmentPhase =
  | 'initial'
  | 'confirmation'
  | 'comparison'
  | 'complete';

// Loading states
export type LoadingState = 'idle' | 'loading' | 'generating' | 'submitting';

// Assessment configuration
export interface AssessmentConfig {
  questionsPerPhase: {
    initial: number;
    confirmation: number;
    comparison: number;
  };
  maxRetries: number;
  retryDelayMs: number;
  aiTimeoutMs: number;
  progressSaveIntervalMs: number;
}

// Retry configuration
export interface RetryConfig {
  maxRetries: number;
  baseDelayMs: number;
  maxDelayMs: number;
  backoffMultiplier: number;
}

// Assessment state interface
export interface AssessmentState {
  // Core state
  phase: AssessmentPhase;
  questions: AssessmentQuestion[];
  currentQuestionIndex: number;
  responses: number[]; // Temperament indices selected
  selectedAnswer: number | null;

  // Scoring state
  intermediateScores: TemperamentScores;
  topTemperaments: TemperamentType[];
  primaryTemperament: TemperamentType | null;
  compatibleTemperaments: TemperamentType[];
  result: AssessmentResult | null;

  // Loading states
  isLoading: boolean;
  isGeneratingQuestions: boolean;
  isSubmitting: boolean;

  // Error handling
  error: AssessmentError | null;
  aiError: string | null; // Legacy - will be migrated to AssessmentError

  // Configuration
  isRetake: boolean;
  useAI: boolean;
  isDemo: boolean;
  config: AssessmentConfig;
}

// Action types for better type safety
export interface AssessmentActions {
  // Initialization
  initializeAssessment: () => Promise<void>;
  loadInitialQuestions: () => Promise<void>;
  loadDemoAssessment: () => void;

  // Question generation
  generateConfirmationQuestions: () => Promise<void>;
  generateComparisonQuestions: () => Promise<void>;
  regenerateQuestions: () => Promise<void>;
  useFallbackQuestions: () => void;

  // Navigation and interaction
  handleAnswerSelect: (answerIndex: number) => void;
  handleNextQuestion: () => Promise<void>;
  handlePreviousQuestion: () => void;

  // Scoring and results
  calculateIntermediateScores: () => void;
  calculateResults: () => void;
  submitAssessment: () => Promise<void>;

  // Utility actions
  resetAssessment: () => void;
  clearError: () => void;
  getProgressPercentage: () => number;

  // Error handling actions
  setError: (error: AssessmentError) => void;
  retryAction: (action: () => Promise<void>) => Promise<void>;
}

// Combined store interface
export interface AssessmentStore extends AssessmentState, AssessmentActions {}

// API response types
export interface OpenRouterResponse {
  choices: {
    message: {
      content: string;
    };
  }[];
  error?: {
    message: string;
    code: string;
  };
}

// Question generation request
export interface QuestionGenerationRequest {
  phase: AssessmentPhase;
  topTemperaments?: TemperamentType[];
  primaryTemperament?: TemperamentType;
  compatibleTemperaments?: TemperamentType[];
  previousResponses?: number[];
}

// Validation schemas (will be implemented with Zod)
export interface ValidationSchemas {
  assessmentQuestion: any; // Will be replaced with Zod schema
  assessmentResult: any; // Will be replaced with Zod schema
  temperamentScores: any; // Will be replaced with Zod schema
}
