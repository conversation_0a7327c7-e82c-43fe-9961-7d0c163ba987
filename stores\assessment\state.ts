import { AssessmentState } from './types';
import { ASSESSMENT_CONFIG, DEFAULT_SCORES } from './constants';

// Initial state for the assessment store
export const createInitialState = (): Omit<AssessmentState, keyof import('./types').AssessmentActions> => ({
  // Core state
  phase: 'initial',
  questions: [],
  currentQuestionIndex: 0,
  responses: [],
  selectedAnswer: null,
  
  // Scoring state
  intermediateScores: { ...DEFAULT_SCORES },
  topTemperaments: [],
  primaryTemperament: null,
  compatibleTemperaments: [],
  result: null,
  
  // Loading states
  isLoading: false,
  isGeneratingQuestions: false,
  isSubmitting: false,
  
  // Error handling
  error: null,
  aiError: null, // Legacy - will be migrated
  
  // Configuration
  isRetake: false,
  useAI: true,
  isDemo: false,
  config: ASSESSMENT_CONFIG,
});

// Reset state for retaking assessment
export const createResetState = (): Partial<AssessmentState> => ({
  phase: 'initial',
  questions: [],
  currentQuestionIndex: 0,
  responses: [],
  selectedAnswer: null,
  intermediateScores: { ...DEFAULT_SCORES },
  topTemperaments: [],
  primaryTemperament: null,
  compatibleTemperaments: [],
  result: null,
  isLoading: true,
  isGeneratingQuestions: false,
  isSubmitting: false,
  error: null,
  aiError: null,
});

// State for demo mode
export const createDemoState = (): Partial<AssessmentState> => ({
  isDemo: true,
  useAI: false,
  isLoading: false,
  error: null,
  aiError: null,
});

// State for loading
export const createLoadingState = (): Partial<AssessmentState> => ({
  isLoading: true,
  isGeneratingQuestions: false,
  isSubmitting: false,
  error: null,
});

// State for question generation
export const createGeneratingState = (): Partial<AssessmentState> => ({
  isLoading: false,
  isGeneratingQuestions: true,
  isSubmitting: false,
  error: null,
});

// State for submission
export const createSubmittingState = (): Partial<AssessmentState> => ({
  isLoading: false,
  isGeneratingQuestions: false,
  isSubmitting: true,
  error: null,
});

// State for completion
export const createCompletionState = (): Partial<AssessmentState> => ({
  phase: 'complete',
  isLoading: false,
  isGeneratingQuestions: false,
  isSubmitting: false,
  error: null,
  selectedAnswer: null,
});

// State for error
export const createErrorState = (error: AssessmentState['error']): Partial<AssessmentState> => ({
  isLoading: false,
  isGeneratingQuestions: false,
  isSubmitting: false,
  error,
});

// Utility functions for state validation
export const isValidState = (state: Partial<AssessmentState>): boolean => {
  // Basic validation rules
  if (state.currentQuestionIndex !== undefined && state.questions) {
    if (state.currentQuestionIndex < 0 || state.currentQuestionIndex >= state.questions.length) {
      return false;
    }
  }
  
  if (state.responses && state.currentQuestionIndex !== undefined) {
    if (state.responses.length > state.currentQuestionIndex + 1) {
      return false;
    }
  }
  
  return true;
};

// Get current question safely
export const getCurrentQuestion = (state: AssessmentState) => {
  if (!state.questions.length || state.currentQuestionIndex >= state.questions.length) {
    return null;
  }
  return state.questions[state.currentQuestionIndex];
};

// Check if can navigate back
export const canNavigateBack = (state: AssessmentState): boolean => {
  return state.currentQuestionIndex > 0 && !state.isSubmitting;
};

// Check if can proceed to next question
export const canProceed = (state: AssessmentState): boolean => {
  return state.selectedAnswer !== null && !state.isSubmitting;
};

// Check if is first question
export const isFirstQuestion = (state: AssessmentState): boolean => {
  return state.currentQuestionIndex === 0;
};

// Check if is last question
export const isLastQuestion = (state: AssessmentState): boolean => {
  return state.currentQuestionIndex === state.questions.length - 1;
};

// Get total questions count
export const getTotalQuestions = (state: AssessmentState): number => {
  const { initial, confirmation, comparison } = state.config.questionsPerPhase;
  return initial + confirmation + comparison;
};

// Get completed questions count
export const getCompletedQuestions = (state: AssessmentState): number => {
  return state.responses.length;
};

// Calculate progress percentage
export const calculateProgress = (state: AssessmentState): number => {
  const totalQuestions = getTotalQuestions(state);
  const completedQuestions = getCompletedQuestions(state);
  
  if (totalQuestions === 0) return 0;
  
  return Math.round((completedQuestions / totalQuestions) * 100);
};

// Check if assessment is complete
export const isAssessmentComplete = (state: AssessmentState): boolean => {
  return state.phase === 'complete' && state.result !== null;
};

// Check if in demo mode
export const isDemoMode = (state: AssessmentState): boolean => {
  return state.isDemo;
};

// Check if using AI
export const isUsingAI = (state: AssessmentState): boolean => {
  return state.useAI && !state.isDemo;
};

// Check if has error
export const hasError = (state: AssessmentState): boolean => {
  return state.error !== null || state.aiError !== null;
};

// Check if is loading
export const isLoading = (state: AssessmentState): boolean => {
  return state.isLoading || state.isGeneratingQuestions || state.isSubmitting;
};
