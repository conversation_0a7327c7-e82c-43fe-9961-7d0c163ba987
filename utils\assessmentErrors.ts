import { ERROR_TYPES, ERROR_MESSAGES, ASSESSMENT_CONFIG } from '@/constants/assessment';

export interface AssessmentError {
  type: keyof typeof ERROR_TYPES;
  message: string;
  retryable: boolean;
  context?: Record<string, unknown>;
  timestamp: number;
}

export class AssessmentErrorHandler {
  private static retryAttempts = new Map<string, number>();

  static createError(
    type: keyof typeof ERROR_TYPES,
    message?: string,
    context?: Record<string, unknown>
  ): AssessmentError {
    return {
      type,
      message: message || ERROR_MESSAGES[`${type.toUpperCase()}_ERROR` as keyof typeof ERROR_MESSAGES] || 'An unexpected error occurred',
      retryable: this.isRetryable(type),
      context,
      timestamp: Date.now(),
    };
  }

  static isRetryable(errorType: keyof typeof ERROR_TYPES): boolean {
    const retryableErrors = ['NETWORK', 'AI_GENERATION', 'SUBMISSION'];
    return retryableErrors.includes(errorType);
  }

  static canRetry(operationId: string): boolean {
    const attempts = this.retryAttempts.get(operationId) || 0;
    return attempts < ASSESSMENT_CONFIG.MAX_RETRIES;
  }

  static incrementRetryCount(operationId: string): number {
    const currentAttempts = this.retryAttempts.get(operationId) || 0;
    const newAttempts = currentAttempts + 1;
    this.retryAttempts.set(operationId, newAttempts);
    return newAttempts;
  }

  static resetRetryCount(operationId: string): void {
    this.retryAttempts.delete(operationId);
  }

  static async retryWithBackoff<T>(
    operation: () => Promise<T>,
    operationId: string,
    onRetry?: (attempt: number) => void
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 0; attempt <= ASSESSMENT_CONFIG.MAX_RETRIES; attempt++) {
      try {
        if (attempt > 0) {
          // Exponential backoff: 1s, 2s, 4s
          const delay = ASSESSMENT_CONFIG.RETRY_DELAY_MS * Math.pow(2, attempt - 1);
          await this.delay(delay);
          onRetry?.(attempt);
        }

        const result = await operation();
        this.resetRetryCount(operationId);
        return result;
      } catch (error) {
        lastError = error as Error;
        this.incrementRetryCount(operationId);
        
        if (attempt === ASSESSMENT_CONFIG.MAX_RETRIES) {
          break;
        }
      }
    }

    throw this.createError('NETWORK', `Operation failed after ${ASSESSMENT_CONFIG.MAX_RETRIES} retries: ${lastError.message}`, {
      operationId,
      originalError: lastError.message,
    });
  }

  static async withTimeout<T>(
    operation: () => Promise<T>,
    timeoutMs: number = ASSESSMENT_CONFIG.AI_TIMEOUT_MS
  ): Promise<T> {
    return Promise.race([
      operation(),
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(this.createError('NETWORK', 'Operation timed out')), timeoutMs)
      ),
    ]);
  }

  private static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  static getErrorMessage(error: AssessmentError): string {
    switch (error.type) {
      case 'NETWORK':
        return ERROR_MESSAGES.NETWORK_ERROR;
      case 'AI_GENERATION':
        return ERROR_MESSAGES.AI_GENERATION_FAILED;
      case 'SUBMISSION':
        return ERROR_MESSAGES.SUBMISSION_FAILED;
      case 'AUTHENTICATION':
        return ERROR_MESSAGES.AUTHENTICATION_REQUIRED;
      case 'VALIDATION':
        return ERROR_MESSAGES.INVALID_RESPONSE;
      default:
        return error.message;
    }
  }

  static getRecoveryActions(error: AssessmentError): Array<{
    label: string;
    action: string;
    primary?: boolean;
  }> {
    const actions = [];

    if (error.retryable) {
      actions.push({
        label: 'Try Again',
        action: 'retry',
        primary: true,
      });
    }

    switch (error.type) {
      case 'AI_GENERATION':
        actions.push({
          label: 'Use Standard Questions',
          action: 'fallback',
        });
        break;
      case 'NETWORK':
        actions.push({
          label: 'Check Connection',
          action: 'check_network',
        });
        break;
      case 'AUTHENTICATION':
        actions.push({
          label: 'Sign In',
          action: 'authenticate',
          primary: true,
        });
        break;
    }

    return actions;
  }

  static logError(error: AssessmentError): void {
    console.error('Assessment Error:', {
      type: error.type,
      message: error.message,
      context: error.context,
      timestamp: new Date(error.timestamp).toISOString(),
    });

    // In production, send to error tracking service
    if (__DEV__) {
      console.warn('Error details:', error);
    }
  }
}

// Utility function for handling async operations with error handling
export async function handleAsyncOperation<T>(
  operation: () => Promise<T>,
  operationId: string,
  options: {
    retryable?: boolean;
    timeout?: number;
    onRetry?: (attempt: number) => void;
    onError?: (error: AssessmentError) => void;
  } = {}
): Promise<T> {
  try {
    const { retryable = true, timeout, onRetry, onError } = options;

    let wrappedOperation = operation;

    if (timeout) {
      wrappedOperation = () => AssessmentErrorHandler.withTimeout(operation, timeout);
    }

    if (retryable) {
      return await AssessmentErrorHandler.retryWithBackoff(wrappedOperation, operationId, onRetry);
    } else {
      return await wrappedOperation();
    }
  } catch (error) {
    const assessmentError = error instanceof Error 
      ? AssessmentErrorHandler.createError('NETWORK', error.message, { operationId })
      : error as AssessmentError;

    AssessmentErrorHandler.logError(assessmentError);
    options.onError?.(assessmentError);
    throw assessmentError;
  }
}
