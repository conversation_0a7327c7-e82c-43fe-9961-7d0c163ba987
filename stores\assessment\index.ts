import { create } from 'zustand';
import { AssessmentStore } from './types';
import { createInitialState } from './state';
import { createAssessmentActions } from './actions';
import { withMiddleware, withPersistence } from './middleware';

// Create the assessment store with all middleware
export const useAssessmentStore = create<AssessmentStore>()(
  withPersistence(
    withMiddleware((set, get, api) => ({
      // Initial state
      ...createInitialState(),

      // Actions
      ...createAssessmentActions(set, get, api),
    }))
  )
);

// Export types for external use
export type {
  AssessmentStore,
  AssessmentState,
  AssessmentActions,
  AssessmentError,
  AssessmentQuestion,
  AssessmentQuestions,
  AssessmentResult,
  TemperamentScores,
  AssessmentPhase,
  LoadingState,
  AssessmentConfig,
  RetryConfig,
} from './types';

// Export constants
export {
  TEMPERAMENT_MAPPING,
  TEMPERAMENT_COMPATIBILITY,
  ASSESSMENT_CONFIG,
  RETRY_CONFIG,
  LOADING_STATES,
  ERROR_TYPES,
  ASSESSMENT_PHASES,
  QUESTION_CATEGORIES,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  DEFAULT_SCORES,
  TEMPERAMENT_NAMES,
  TEMPERAMENT_DESCRIPTIONS,
} from './constants';

// Export selectors
export {
  selectCurrentQuestion,
  selectNavigationState,
  selectLoadingState,
  selectErrorState,
  selectAssessmentConfig,
  selectProgressInfo,
  selectScoringInfo,
  selectWeightedScores,
  selectSortedTemperaments,
  selectConfidenceLevel,
  selectHasTie,
  selectRecommendedAction,
  selectAssessmentSummary,
  selectQuestionStats,
  selectPhaseInfo,
} from './selectors';

// Export error handling utilities
export {
  createAssessmentError,
  createNetworkError,
  createAIGenerationError,
  createSubmissionError,
  createAuthenticationError,
  createValidationError,
  createTimeoutError,
  createGenericError,
  retryWithBackoff,
  retryWithTimeout,
  isRetryableError,
  normalizeError,
  getErrorRecoveryStrategy,
  logError,
  createErrorHandler,
} from './errorHandling';

// Export middleware utilities
export {
  clearPersistedData,
  getPersistedDataSize,
  validatePersistedData,
} from './middleware';

// Export state utilities
export {
  createResetState,
  createDemoState,
  createLoadingState,
  createGeneratingState,
  createSubmittingState,
  createCompletionState,
  createErrorState,
  isValidState,
  getCurrentQuestion,
  canNavigateBack,
  canProceed,
  isFirstQuestion,
  isLastQuestion,
  getTotalQuestions,
  getCompletedQuestions,
  calculateProgress,
  isAssessmentComplete,
  isDemoMode,
  isUsingAI,
  hasError,
  isLoading,
} from './state';

// Convenience hooks for common use cases

// Hook for navigation state
export const useAssessmentNavigation = () => {
  return useAssessmentStore((state) => ({
    canGoBack: state.currentQuestionIndex > 0 && !state.isSubmitting,
    canProceed: state.selectedAnswer !== null && !state.isSubmitting,
    isFirst: state.currentQuestionIndex === 0,
    isLast: state.currentQuestionIndex === state.questions.length - 1,
    currentIndex: state.currentQuestionIndex,
    totalQuestions: state.questions.length,
    handlePrevious: state.handlePreviousQuestion,
    handleNext: state.handleNextQuestion,
  }));
};

// Hook for current question
export const useCurrentQuestion = () => {
  return useAssessmentStore((state) => ({
    question: state.questions[state.currentQuestionIndex] || null,
    selectedAnswer: state.selectedAnswer,
    handleAnswerSelect: state.handleAnswerSelect,
  }));
};

// Hook for loading states
export const useAssessmentLoading = () => {
  return useAssessmentStore((state) => ({
    isLoading: state.isLoading,
    isGenerating: state.isGeneratingQuestions,
    isSubmitting: state.isSubmitting,
    anyLoading:
      state.isLoading || state.isGeneratingQuestions || state.isSubmitting,
  }));
};

// Hook for error handling
export const useAssessmentErrors = () => {
  return useAssessmentStore((state) => ({
    error: state.error,
    aiError: state.aiError,
    hasError: state.error !== null || state.aiError !== null,
    clearError: state.clearError,
    retryAction: state.retryAction,
  }));
};

// Hook for progress tracking
export const useAssessmentProgress = () => {
  return useAssessmentStore((state) => {
    const totalQuestions =
      state.config.questionsPerPhase.initial +
      state.config.questionsPerPhase.confirmation +
      state.config.questionsPerPhase.comparison;
    const completedQuestions = state.responses.length;
    const percentage =
      totalQuestions > 0
        ? Math.round((completedQuestions / totalQuestions) * 100)
        : 0;

    return {
      percentage,
      completed: completedQuestions,
      total: totalQuestions,
      phase: state.phase,
      currentQuestion: state.currentQuestionIndex + 1,
      totalQuestions: state.questions.length,
    };
  });
};

// Hook for assessment configuration
export const useAssessmentConfig = () => {
  return useAssessmentStore((state) => ({
    isRetake: state.isRetake,
    useAI: state.useAI,
    isDemo: state.isDemo,
    config: state.config,
  }));
};

// Hook for scoring information
export const useAssessmentScoring = () => {
  return useAssessmentStore((state) => ({
    intermediateScores: state.intermediateScores,
    topTemperaments: state.topTemperaments,
    primaryTemperament: state.primaryTemperament,
    compatibleTemperaments: state.compatibleTemperaments,
    result: state.result,
    isComplete: state.phase === 'complete' && state.result !== null,
  }));
};

// Hook for assessment actions
export const useAssessmentActions = () => {
  return useAssessmentStore((state) => ({
    initialize: state.initializeAssessment,
    regenerate: state.regenerateQuestions,
    useFallback: state.useFallbackQuestions,
    reset: state.resetAssessment,
    submit: state.submitAssessment,
  }));
};

// Development utilities
export const assessmentStoreDevUtils = {
  // Get full store state (for debugging)
  getState: () => useAssessmentStore.getState(),

  // Subscribe to store changes
  subscribe: useAssessmentStore.subscribe,

  // Reset store to initial state
  reset: () => {
    useAssessmentStore.getState().resetAssessment();
  },

  // Force error state (for testing)
  forceError: (error: string) => {
    useAssessmentStore.getState().setError({
      type: 'network',
      message: error,
      retryable: true,
      timestamp: new Date().toISOString(),
    });
  },

  // Get store statistics
  getStats: () => {
    const state = useAssessmentStore.getState();
    const totalQuestions =
      state.config.questionsPerPhase.initial +
      state.config.questionsPerPhase.confirmation +
      state.config.questionsPerPhase.comparison;
    const completedQuestions = state.responses.length;
    const progressPercentage =
      totalQuestions > 0
        ? Math.round((completedQuestions / totalQuestions) * 100)
        : 0;

    return {
      questionsLoaded: state.questions.length,
      responsesGiven: state.responses.length,
      currentPhase: state.phase,
      progressPercentage,
      hasErrors: state.error !== null || state.aiError !== null,
      isDemo: state.isDemo,
      useAI: state.useAI,
    };
  },
};

// Make dev utils available in development
if (typeof __DEV__ !== 'undefined' && __DEV__) {
  (globalThis as any).assessmentStore = assessmentStoreDevUtils;
  console.log(
    '🛠️ Assessment store dev utils available at globalThis.assessmentStore'
  );
}
