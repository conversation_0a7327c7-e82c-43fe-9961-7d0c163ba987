import { AssessmentError } from './types';
import { ERROR_TYPES, ERROR_MESSAGES, RETRY_CONFIG } from './constants';

// Create a standardized error
export const createAssessmentError = (
  type: AssessmentError['type'],
  message: string,
  retryable: boolean = true,
  context?: Record<string, any>
): AssessmentError => ({
  type,
  message,
  retryable,
  context,
  timestamp: new Date().toISOString(),
});

// Create specific error types
export const createNetworkError = (originalError?: any): AssessmentError => 
  createAssessmentError(
    ERROR_TYPES.NETWORK,
    ERROR_MESSAGES.NETWORK_ERROR,
    true,
    { originalError: originalError?.message || originalError }
  );

export const createAIGenerationError = (originalError?: any): AssessmentError =>
  createAssessmentError(
    ERROR_TYPES.AI_GENERATION,
    ERROR_MESSAGES.AI_GENERATION_FAILED,
    true,
    { originalError: originalError?.message || originalError }
  );

export const createSubmissionError = (originalError?: any): AssessmentError =>
  createAssessmentError(
    ERROR_TYPES.SUBMISSION,
    ERROR_MESSAGES.SUBMISSION_FAILED,
    true,
    { originalError: originalError?.message || originalError }
  );

export const createAuthenticationError = (): AssessmentError =>
  createAssessmentError(
    ERROR_TYPES.AUTHENTICATION,
    ERROR_MESSAGES.AUTHENTICATION_REQUIRED,
    false
  );

export const createValidationError = (details?: string): AssessmentError =>
  createAssessmentError(
    ERROR_TYPES.VALIDATION,
    details || ERROR_MESSAGES.VALIDATION_ERROR,
    false,
    { details }
  );

export const createTimeoutError = (): AssessmentError =>
  createAssessmentError(
    ERROR_TYPES.NETWORK,
    ERROR_MESSAGES.TIMEOUT_ERROR,
    true
  );

export const createGenericError = (message?: string): AssessmentError =>
  createAssessmentError(
    ERROR_TYPES.NETWORK,
    message || ERROR_MESSAGES.GENERIC_ERROR,
    true
  );

// Retry logic with exponential backoff
export const retryWithBackoff = async <T>(
  fn: () => Promise<T>,
  config: typeof RETRY_CONFIG = RETRY_CONFIG
): Promise<T> => {
  let lastError: Error;
  
  for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      
      // Don't retry on the last attempt
      if (attempt === config.maxRetries) {
        break;
      }
      
      // Calculate delay with exponential backoff
      const delay = Math.min(
        config.baseDelayMs * Math.pow(config.backoffMultiplier, attempt),
        config.maxDelayMs
      );
      
      console.warn(`Attempt ${attempt + 1} failed, retrying in ${delay}ms:`, error);
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError!;
};

// Retry with timeout
export const retryWithTimeout = async <T>(
  fn: () => Promise<T>,
  timeoutMs: number = 30000,
  retryConfig: typeof RETRY_CONFIG = RETRY_CONFIG
): Promise<T> => {
  return Promise.race([
    retryWithBackoff(fn, retryConfig),
    new Promise<never>((_, reject) => 
      setTimeout(() => reject(createTimeoutError()), timeoutMs)
    )
  ]);
};

// Check if error is retryable
export const isRetryableError = (error: AssessmentError | Error | any): boolean => {
  if (error && typeof error === 'object' && 'retryable' in error) {
    return error.retryable;
  }
  
  // Check for common retryable error patterns
  if (error?.message) {
    const message = error.message.toLowerCase();
    return (
      message.includes('network') ||
      message.includes('timeout') ||
      message.includes('connection') ||
      message.includes('fetch') ||
      message.includes('502') ||
      message.includes('503') ||
      message.includes('504')
    );
  }
  
  return true; // Default to retryable for unknown errors
};

// Convert any error to AssessmentError
export const normalizeError = (error: any): AssessmentError => {
  if (error && typeof error === 'object' && 'type' in error && 'message' in error) {
    return error as AssessmentError;
  }
  
  if (error instanceof Error) {
    // Determine error type based on error message/type
    if (error.message.toLowerCase().includes('network') || 
        error.message.toLowerCase().includes('fetch')) {
      return createNetworkError(error);
    }
    
    if (error.message.toLowerCase().includes('timeout')) {
      return createTimeoutError();
    }
    
    if (error.message.toLowerCase().includes('auth')) {
      return createAuthenticationError();
    }
    
    return createGenericError(error.message);
  }
  
  if (typeof error === 'string') {
    return createGenericError(error);
  }
  
  return createGenericError('An unknown error occurred');
};

// Error recovery strategies
export const getErrorRecoveryStrategy = (error: AssessmentError): {
  action: 'retry' | 'fallback' | 'redirect' | 'manual';
  message: string;
  autoRetry?: boolean;
} => {
  switch (error.type) {
    case ERROR_TYPES.NETWORK:
      return {
        action: 'retry',
        message: 'Check your connection and try again',
        autoRetry: true,
      };
      
    case ERROR_TYPES.AI_GENERATION:
      return {
        action: 'fallback',
        message: 'Use standard questions instead',
        autoRetry: false,
      };
      
    case ERROR_TYPES.AUTHENTICATION:
      return {
        action: 'redirect',
        message: 'Please sign in to continue',
        autoRetry: false,
      };
      
    case ERROR_TYPES.VALIDATION:
      return {
        action: 'manual',
        message: 'Please check your responses',
        autoRetry: false,
      };
      
    case ERROR_TYPES.SUBMISSION:
      return {
        action: 'retry',
        message: 'Try submitting again',
        autoRetry: true,
      };
      
    default:
      return {
        action: 'retry',
        message: 'Try again',
        autoRetry: false,
      };
  }
};

// Log error for debugging/monitoring
export const logError = (error: AssessmentError, context?: Record<string, any>) => {
  const logData = {
    ...error,
    context: { ...error.context, ...context },
    userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
    timestamp: new Date().toISOString(),
  };
  
  console.error('Assessment Error:', logData);
  
  // In production, you might want to send this to an error tracking service
  // like Sentry, LogRocket, etc.
  if (__DEV__) {
    console.table(logData);
  }
};

// Create error handler function
export const createErrorHandler = (
  setError: (error: AssessmentError) => void,
  logErrors: boolean = true
) => {
  return (error: any, context?: Record<string, any>) => {
    const normalizedError = normalizeError(error);
    
    if (logErrors) {
      logError(normalizedError, context);
    }
    
    setError(normalizedError);
  };
};
