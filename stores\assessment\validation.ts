import { z } from 'zod';
import { VALIDATION_RULES } from './constants';

// Temperament type schema
export const TemperamentSchema = z.enum(['choleric', 'sanguine', 'melancholic', 'phlegmatic']);

// Assessment question schema
export const AssessmentQuestionSchema = z.object({
  questionNumber: z.number().min(1),
  category: z.enum(['initial', 'confirmation', 'comparison', 'tiebreaker']),
  question: z.string()
    .min(VALIDATION_RULES.MIN_QUESTION_LENGTH)
    .max(VALIDATION_RULES.MAX_QUESTION_LENGTH),
  answers: z.array(z.string()
    .min(VALIDATION_RULES.MIN_ANSWER_LENGTH)
    .max(VALIDATION_RULES.MAX_ANSWER_LENGTH))
    .min(VALIDATION_RULES.MIN_ANSWERS_PER_QUESTION)
    .max(VALIDATION_RULES.MAX_ANSWERS_PER_QUESTION),
  temperamentOrder: z.array(z.number().min(0).max(3))
    .min(VALIDATION_RULES.MIN_ANSWERS_PER_QUESTION)
    .max(VALIDATION_RULES.MAX_ANSWERS_PER_QUESTION),
});

// Assessment questions collection schema
export const AssessmentQuestionsSchema = z.object({
  instructions: z.string().min(1),
  questions: z.array(AssessmentQuestionSchema)
    .min(1)
    .max(20), // Reasonable maximum
});

// Temperament scores schema
export const TemperamentScoresSchema = z.object({
  choleric: z.number().min(0),
  sanguine: z.number().min(0),
  melancholic: z.number().min(0),
  phlegmatic: z.number().min(0),
});

// Assessment result schema
export const AssessmentResultSchema = z.object({
  primaryTemperament: TemperamentSchema,
  secondaryTemperament: TemperamentSchema,
  primaryPercentage: z.number().min(0).max(100),
  secondaryPercentage: z.number().min(0).max(100),
  temperamentScores: TemperamentScoresSchema,
  completedAt: z.string().datetime(),
});

// Assessment error schema
export const AssessmentErrorSchema = z.object({
  type: z.enum(['network', 'validation', 'ai_generation', 'submission', 'authentication']),
  message: z.string().min(1),
  retryable: z.boolean(),
  context: z.record(z.any()).optional(),
  timestamp: z.string().datetime().optional(),
});

// Assessment configuration schema
export const AssessmentConfigSchema = z.object({
  questionsPerPhase: z.object({
    initial: z.number().min(1).max(10),
    confirmation: z.number().min(1).max(10),
    comparison: z.number().min(1).max(10),
  }),
  maxRetries: z.number().min(1).max(10),
  retryDelayMs: z.number().min(100).max(60000),
  aiTimeoutMs: z.number().min(5000).max(120000),
  progressSaveIntervalMs: z.number().min(1000).max(60000),
});

// Assessment state schema (for validation)
export const AssessmentStateSchema = z.object({
  phase: z.enum(['initial', 'confirmation', 'comparison', 'complete']),
  questions: z.array(AssessmentQuestionSchema),
  currentQuestionIndex: z.number().min(0),
  responses: z.array(z.number().min(0).max(3)),
  selectedAnswer: z.number().min(0).max(3).nullable(),
  intermediateScores: TemperamentScoresSchema,
  topTemperaments: z.array(TemperamentSchema).max(4),
  primaryTemperament: TemperamentSchema.nullable(),
  compatibleTemperaments: z.array(TemperamentSchema).max(4),
  result: AssessmentResultSchema.nullable(),
  isLoading: z.boolean(),
  isGeneratingQuestions: z.boolean(),
  isSubmitting: z.boolean(),
  error: AssessmentErrorSchema.nullable(),
  aiError: z.string().nullable(),
  isRetake: z.boolean(),
  useAI: z.boolean(),
  isDemo: z.boolean(),
  config: AssessmentConfigSchema,
});

// OpenRouter API response schema
export const OpenRouterResponseSchema = z.object({
  choices: z.array(z.object({
    message: z.object({
      content: z.string(),
    }),
  })),
  error: z.object({
    message: z.string(),
    code: z.string(),
  }).optional(),
});

// Validation functions

// Validate assessment question
export const validateAssessmentQuestion = (question: unknown): question is z.infer<typeof AssessmentQuestionSchema> => {
  try {
    AssessmentQuestionSchema.parse(question);
    return true;
  } catch {
    return false;
  }
};

// Validate assessment questions collection
export const validateAssessmentQuestions = (questions: unknown): questions is z.infer<typeof AssessmentQuestionsSchema> => {
  try {
    AssessmentQuestionsSchema.parse(questions);
    return true;
  } catch {
    return false;
  }
};

// Validate assessment result
export const validateAssessmentResult = (result: unknown): result is z.infer<typeof AssessmentResultSchema> => {
  try {
    AssessmentResultSchema.parse(result);
    return true;
  } catch {
    return false;
  }
};

// Validate temperament scores
export const validateTemperamentScores = (scores: unknown): scores is z.infer<typeof TemperamentScoresSchema> => {
  try {
    TemperamentScoresSchema.parse(scores);
    return true;
  } catch {
    return false;
  }
};

// Validate assessment error
export const validateAssessmentError = (error: unknown): error is z.infer<typeof AssessmentErrorSchema> => {
  try {
    AssessmentErrorSchema.parse(error);
    return true;
  } catch {
    return false;
  }
};

// Validate assessment state
export const validateAssessmentState = (state: unknown): state is z.infer<typeof AssessmentStateSchema> => {
  try {
    AssessmentStateSchema.parse(state);
    return true;
  } catch {
    return false;
  }
};

// Safe parsing functions that return results with error information

// Safe parse assessment question
export const safeParseAssessmentQuestion = (question: unknown) => {
  return AssessmentQuestionSchema.safeParse(question);
};

// Safe parse assessment questions
export const safeParseAssessmentQuestions = (questions: unknown) => {
  return AssessmentQuestionsSchema.safeParse(questions);
};

// Safe parse assessment result
export const safeParseAssessmentResult = (result: unknown) => {
  return AssessmentResultSchema.safeParse(result);
};

// Safe parse OpenRouter response
export const safeParseOpenRouterResponse = (response: unknown) => {
  return OpenRouterResponseSchema.safeParse(response);
};

// Validation error formatter
export const formatValidationError = (error: z.ZodError): string => {
  const issues = error.issues.map(issue => {
    const path = issue.path.join('.');
    return `${path}: ${issue.message}`;
  });
  
  return `Validation failed: ${issues.join(', ')}`;
};

// Custom validation rules

// Validate question answers match temperament order
export const validateQuestionConsistency = (question: z.infer<typeof AssessmentQuestionSchema>): boolean => {
  return question.answers.length === question.temperamentOrder.length;
};

// Validate responses are within bounds
export const validateResponses = (responses: number[], questions: z.infer<typeof AssessmentQuestionSchema>[]): boolean => {
  if (responses.length > questions.length) return false;
  
  return responses.every((response, index) => {
    const question = questions[index];
    return question && response >= 0 && response < question.answers.length;
  });
};

// Validate temperament scores sum
export const validateScoresSum = (scores: z.infer<typeof TemperamentScoresSchema>, expectedTotal?: number): boolean => {
  const total = Object.values(scores).reduce((sum, score) => sum + score, 0);
  
  if (expectedTotal !== undefined) {
    return total === expectedTotal;
  }
  
  return total >= 0;
};

// Validate percentages sum to 100 (with tolerance for rounding)
export const validatePercentages = (primaryPercentage: number, secondaryPercentage: number): boolean => {
  const total = primaryPercentage + secondaryPercentage;
  return total >= 95 && total <= 105; // Allow 5% tolerance for rounding
};

// Export all schemas for external use
export const ValidationSchemas = {
  Temperament: TemperamentSchema,
  AssessmentQuestion: AssessmentQuestionSchema,
  AssessmentQuestions: AssessmentQuestionsSchema,
  TemperamentScores: TemperamentScoresSchema,
  AssessmentResult: AssessmentResultSchema,
  AssessmentError: AssessmentErrorSchema,
  AssessmentConfig: AssessmentConfigSchema,
  AssessmentState: AssessmentStateSchema,
  OpenRouterResponse: OpenRouterResponseSchema,
} as const;

// Export type definitions
export type ValidatedAssessmentQuestion = z.infer<typeof AssessmentQuestionSchema>;
export type ValidatedAssessmentQuestions = z.infer<typeof AssessmentQuestionsSchema>;
export type ValidatedAssessmentResult = z.infer<typeof AssessmentResultSchema>;
export type ValidatedTemperamentScores = z.infer<typeof TemperamentScoresSchema>;
export type ValidatedAssessmentError = z.infer<typeof AssessmentErrorSchema>;
export type ValidatedAssessmentState = z.infer<typeof AssessmentStateSchema>;
export type ValidatedOpenRouterResponse = z.infer<typeof OpenRouterResponseSchema>;
