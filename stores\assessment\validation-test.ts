/**
 * Simple validation script for the refactored assessment store
 * This can be run to verify the modular store works correctly
 * Run with: npx tsx stores/assessment/validation-test.ts
 */

import { 
  useAssessmentStore,
  createAssessmentError, 
  createNetworkError,
  validateAssessmentQuestion,
  selectCurrentQuestion,
  selectNavigationState,
  calculateProgress,
  TEMPERAMENT_MAPPING,
  DEFAULT_SCORES,
} from './index';

// Simple test runner
class SimpleTestRunner {
  private tests: Array<{ name: string; fn: () => void | Promise<void> }> = [];
  private passed = 0;
  private failed = 0;

  test(name: string, fn: () => void | Promise<void>) {
    this.tests.push({ name, fn });
  }

  expect(actual: any) {
    return {
      toBe: (expected: any) => {
        if (actual !== expected) {
          throw new Error(`Expected ${expected}, but got ${actual}`);
        }
      },
      toBeDefined: () => {
        if (actual === undefined) {
          throw new Error(`Expected value to be defined, but got undefined`);
        }
      },
      toEqual: (expected: any) => {
        if (JSON.stringify(actual) !== JSON.stringify(expected)) {
          throw new Error(`Expected ${JSON.stringify(expected)}, but got ${JSON.stringify(actual)}`);
        }
      },
      toBeNull: () => {
        if (actual !== null) {
          throw new Error(`Expected null, but got ${actual}`);
        }
      },
      toHaveProperty: (prop: string) => {
        if (!(prop in actual)) {
          throw new Error(`Expected object to have property ${prop}`);
        }
      },
    };
  }

  async run() {
    console.log('🧪 Running Assessment Store Validation Tests...\n');

    for (const test of this.tests) {
      try {
        await test.fn();
        console.log(`✅ ${test.name}`);
        this.passed++;
      } catch (error) {
        console.log(`❌ ${test.name}: ${error.message}`);
        this.failed++;
      }
    }

    console.log(`\n📊 Test Results: ${this.passed} passed, ${this.failed} failed`);
    
    if (this.failed === 0) {
      console.log('🎉 All tests passed! The refactored assessment store is working correctly.');
    } else {
      console.log('⚠️ Some tests failed. Please check the implementation.');
    }
  }
}

const runner = new SimpleTestRunner();

// Test store structure
runner.test('Store should have all required state properties', () => {
  const state = useAssessmentStore.getState();
  
  // Core state
  runner.expect(state.phase).toBeDefined();
  runner.expect(state.questions).toBeDefined();
  runner.expect(state.currentQuestionIndex).toBeDefined();
  runner.expect(state.responses).toBeDefined();
  runner.expect(state.selectedAnswer).toBeDefined();
  
  // Scoring state
  runner.expect(state.intermediateScores).toBeDefined();
  runner.expect(state.topTemperaments).toBeDefined();
  runner.expect(state.primaryTemperament).toBeDefined();
  runner.expect(state.compatibleTemperaments).toBeDefined();
  runner.expect(state.result).toBeDefined();
  
  // Loading states
  runner.expect(state.isLoading).toBeDefined();
  runner.expect(state.isGeneratingQuestions).toBeDefined();
  runner.expect(state.isSubmitting).toBeDefined();
  
  // Error handling
  runner.expect(state.error).toBeDefined();
  runner.expect(state.aiError).toBeDefined();
  
  // Configuration
  runner.expect(state.isRetake).toBeDefined();
  runner.expect(state.useAI).toBeDefined();
  runner.expect(state.isDemo).toBeDefined();
  runner.expect(state.config).toBeDefined();
});

// Test action methods
runner.test('Store should have all required action methods', () => {
  const state = useAssessmentStore.getState();
  
  // Check that all action methods exist and are functions
  const actionMethods = [
    'initializeAssessment',
    'loadInitialQuestions',
    'loadDemoAssessment',
    'generateConfirmationQuestions',
    'generateComparisonQuestions',
    'regenerateQuestions',
    'useFallbackQuestions',
    'handleAnswerSelect',
    'handleNextQuestion',
    'handlePreviousQuestion',
    'calculateIntermediateScores',
    'calculateResults',
    'submitAssessment',
    'resetAssessment',
    'clearError',
    'getProgressPercentage',
    'setError',
    'retryAction',
  ];

  actionMethods.forEach(method => {
    runner.expect(typeof (state as any)[method]).toBe('function');
  });
});

// Test error handling
runner.test('Error handling should work correctly', () => {
  const networkError = createNetworkError(new Error('Connection failed'));
  
  runner.expect(networkError.type).toBe('network');
  runner.expect(networkError.message).toBe('Network connection failed. Please check your internet connection.');
  runner.expect(networkError.retryable).toBe(true);
  runner.expect(networkError.context).toBeDefined();
  runner.expect(networkError.timestamp).toBeDefined();
});

runner.test('Generic assessment errors should be created correctly', () => {
  const error = createAssessmentError('validation', 'Invalid input', false, { field: 'test' });
  
  runner.expect(error.type).toBe('validation');
  runner.expect(error.message).toBe('Invalid input');
  runner.expect(error.retryable).toBe(false);
  runner.expect(error.context).toEqual({ field: 'test' });
  runner.expect(error.timestamp).toBeDefined();
});

// Test validation
runner.test('Assessment question validation should work', () => {
  const validQuestion = {
    questionNumber: 1,
    category: 'initial' as const,
    question: 'Test question with sufficient length',
    answers: ['Answer A', 'Answer B', 'Answer C', 'Answer D'],
    temperamentOrder: [0, 1, 2, 3],
  };
  
  runner.expect(validateAssessmentQuestion(validQuestion)).toBe(true);
});

runner.test('Invalid assessment questions should be rejected', () => {
  const invalidQuestion = {
    questionNumber: 1,
    category: 'invalid' as any,
    question: 'Short',
    answers: ['A'],
    temperamentOrder: [0],
  };
  
  runner.expect(validateAssessmentQuestion(invalidQuestion)).toBe(false);
});

// Test selectors
runner.test('Progress calculation should work', () => {
  const state = useAssessmentStore.getState();
  
  // Initial state should have 0% progress
  runner.expect(calculateProgress(state)).toBe(0);
});

runner.test('Current question selector should work safely', () => {
  const state = useAssessmentStore.getState();
  
  // No questions loaded initially
  runner.expect(selectCurrentQuestion(state)).toBeNull();
});

runner.test('Navigation state should be calculated correctly', () => {
  const state = useAssessmentStore.getState();
  const navState = selectNavigationState(state);
  
  runner.expect(navState.canGoBack).toBe(false);
  runner.expect(navState.canProceed).toBe(false);
  runner.expect(navState.isFirst).toBe(true);
  runner.expect(navState.isLast).toBe(false);
  runner.expect(navState.currentIndex).toBe(0);
  runner.expect(navState.totalQuestions).toBe(0);
});

// Test state management
runner.test('Answer selection should work', () => {
  const store = useAssessmentStore.getState();
  
  store.handleAnswerSelect(2);
  
  const state = useAssessmentStore.getState();
  runner.expect(state.selectedAnswer).toBe(2);
});

runner.test('Assessment reset should work correctly', () => {
  const store = useAssessmentStore.getState();
  
  // Make some changes
  store.handleAnswerSelect(1);
  store.setError(createAssessmentError('network', 'Test error'));
  
  // Reset
  store.resetAssessment();
  
  const state = useAssessmentStore.getState();
  runner.expect(state.selectedAnswer).toBeNull();
  runner.expect(state.error).toBeNull();
  runner.expect(state.currentQuestionIndex).toBe(0);
  runner.expect(state.responses).toEqual([]);
});

// Test configuration
runner.test('Default configuration should be correct', () => {
  const state = useAssessmentStore.getState();
  
  runner.expect(state.config.questionsPerPhase.initial).toBe(4);
  runner.expect(state.config.questionsPerPhase.confirmation).toBe(2);
  runner.expect(state.config.questionsPerPhase.comparison).toBe(3);
  runner.expect(state.config.maxRetries).toBe(3);
  runner.expect(state.config.retryDelayMs).toBe(1000);
  runner.expect(state.config.aiTimeoutMs).toBe(30000);
  runner.expect(state.config.progressSaveIntervalMs).toBe(5000);
});

// Test constants
runner.test('Constants should be properly defined', () => {
  runner.expect(TEMPERAMENT_MAPPING[0]).toBe('choleric');
  runner.expect(TEMPERAMENT_MAPPING[1]).toBe('sanguine');
  runner.expect(TEMPERAMENT_MAPPING[2]).toBe('melancholic');
  runner.expect(TEMPERAMENT_MAPPING[3]).toBe('phlegmatic');
  
  runner.expect(DEFAULT_SCORES.choleric).toBe(0);
  runner.expect(DEFAULT_SCORES.sanguine).toBe(0);
  runner.expect(DEFAULT_SCORES.melancholic).toBe(0);
  runner.expect(DEFAULT_SCORES.phlegmatic).toBe(0);
});

// Run all tests
if (require.main === module) {
  runner.run().catch(console.error);
}

export { runner };
