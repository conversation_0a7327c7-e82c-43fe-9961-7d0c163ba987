import { StateCreator } from 'zustand';
import { AssessmentStore, AssessmentActions } from './types';
import {
  createResetState,
  createDemoState,
  createLoadingState,
  createGeneratingState,
  createSubmittingState,
  createCompletionState,
  calculateProgress,
  isLastQuestion,
} from './state';
import {
  createSubmissionError,
  createAuthenticationError,
  retryWithTimeout,
  createErrorHandler,
} from './errorHandling';
import {
  TEMPERAMENT_MAPPING,
  TEMPERAMENT_COMPATIBILITY,
  DEFAULT_SCORES,
} from './constants';
import { TemperamentType } from '../../types/personality';

// Import external dependencies (these would be imported from your actual services)
declare const supabase: any;
declare const openRouterService: any;
declare const useAuthStore: any;
declare const router: any;
declare const isSupabaseConfigured: () => boolean;

// Create actions for the assessment store
export const createAssessmentActions: StateCreator<
  AssessmentStore,
  [],
  [],
  AssessmentActions
> = (set, get) => {
  // Create error handler
  const handleError = createErrorHandler((error) => set({ error }));

  return {
    // Initialize the assessment
    initializeAssessment: async () => {
      try {
        set({
          ...createLoadingState(),
          ...createResetState(),
        });

        // Check if user is authenticated
        if (!isSupabaseConfigured() || useAuthStore.getState().isDemo) {
          get().loadDemoAssessment();
          return;
        }

        const {
          data: { user },
        } = await supabase.auth.getUser();
        if (!user) {
          handleError(createAuthenticationError());
          router.replace('/auth');
          return;
        }

        // Check if this is a retake
        const { data: existingProfile } = await supabase
          .from('personality_profiles')
          .select('*')
          .eq('user_id', user.id)
          .single();

        if (existingProfile) {
          set({ isRetake: true });

          // Delete existing profile for retake
          await supabase
            .from('personality_profiles')
            .delete()
            .eq('user_id', user.id);
        }

        await get().loadInitialQuestions();
      } catch (error) {
        handleError(error, { action: 'initializeAssessment' });
      }
    },

    // Load initial questions
    loadInitialQuestions: async () => {
      try {
        set(createGeneratingState());

        let assessmentQuestions;
        const { useAI } = get();

        if (useAI && openRouterService.isConfigured()) {
          try {
            const aiQuestions = await retryWithTimeout(
              () => openRouterService.generateAssessmentQuestions(),
              30000
            );

            console.log('✅ AI questions generated successfully');
            set({ aiError: null });

            // Only keep initial phase questions
            assessmentQuestions = {
              instructions: (aiQuestions as any)?.instructions || '',
              questions: ((aiQuestions as any)?.questions || [])
                .slice(0, 4)
                .map((q: any, idx: number) => ({
                  ...q,
                  questionNumber: idx + 1,
                  category: 'initial',
                })),
            };
          } catch (aiError: any) {
            console.warn('⚠️ AI generation failed:', aiError.message);
            set({ aiError: aiError.message });
            assessmentQuestions = openRouterService.getFallbackQuestions();
            assessmentQuestions.questions = assessmentQuestions.questions.slice(
              0,
              4
            );
          }
        } else {
          assessmentQuestions = openRouterService.getFallbackQuestions();
          assessmentQuestions.questions = assessmentQuestions.questions.slice(
            0,
            4
          );
        }

        set({
          questions: assessmentQuestions.questions,
          isGeneratingQuestions: false,
          isLoading: false,
        });
      } catch (error) {
        handleError(error, { action: 'loadInitialQuestions' });
        set({ isGeneratingQuestions: false, isLoading: false });
      }
    },

    // Generate confirmation questions
    generateConfirmationQuestions: async () => {
      try {
        set(createGeneratingState());

        const { topTemperaments, useAI } = get();
        let newQuestions;

        if (useAI && openRouterService.isConfigured()) {
          try {
            newQuestions = await retryWithTimeout(
              () =>
                openRouterService.generateConfirmationQuestions(
                  topTemperaments
                ),
              30000
            );
          } catch (error) {
            console.warn(
              '⚠️ Confirmation questions generation failed, using fallback'
            );
            newQuestions =
              openRouterService.getFallbackConfirmationQuestions(
                topTemperaments
              );
          }
        } else {
          newQuestions =
            openRouterService.getFallbackConfirmationQuestions(topTemperaments);
        }

        const { questions } = get();
        const updatedQuestions = [...questions, ...newQuestions];

        set({
          questions: updatedQuestions,
          phase: 'confirmation',
          isGeneratingQuestions: false,
        });
      } catch (error) {
        handleError(error, { action: 'generateConfirmationQuestions' });
        set({ isGeneratingQuestions: false });
      }
    },

    // Generate comparison questions
    generateComparisonQuestions: async () => {
      try {
        set(createGeneratingState());

        const { primaryTemperament, compatibleTemperaments, useAI } = get();
        let newQuestions;

        if (useAI && openRouterService.isConfigured()) {
          try {
            newQuestions = await retryWithTimeout(
              () =>
                openRouterService.generateComparisonQuestions(
                  primaryTemperament!,
                  compatibleTemperaments
                ),
              30000
            );
          } catch (error) {
            console.warn(
              '⚠️ Comparison questions generation failed, using fallback'
            );
            newQuestions = openRouterService.getFallbackComparisonQuestions(
              primaryTemperament!,
              compatibleTemperaments
            );
          }
        } else {
          newQuestions = openRouterService.getFallbackComparisonQuestions(
            primaryTemperament!,
            compatibleTemperaments
          );
        }

        const { questions } = get();
        const updatedQuestions = [...questions, ...newQuestions];

        set({
          questions: updatedQuestions,
          phase: 'comparison',
          isGeneratingQuestions: false,
        });
      } catch (error) {
        handleError(error, { action: 'generateComparisonQuestions' });
        set({ isGeneratingQuestions: false });
      }
    },

    // Handle answer selection
    handleAnswerSelect: (answerIndex: number) => {
      set({ selectedAnswer: answerIndex });
    },

    // Handle next question
    handleNextQuestion: async () => {
      try {
        const state = get();
        const {
          selectedAnswer,
          currentQuestionIndex,
          questions,
          responses,
          phase,
        } = state;

        if (selectedAnswer === null) return;

        // Get the current question and temperament mapping
        const currentQuestion = questions[currentQuestionIndex];
        if (!currentQuestion) return;

        const temperamentIndex =
          currentQuestion.temperamentOrder[selectedAnswer];
        const newResponses = [...responses, temperamentIndex];

        // Update responses and move to next question
        set({
          responses: newResponses,
          currentQuestionIndex: currentQuestionIndex + 1,
          selectedAnswer: null,
        });

        // Handle phase transitions and completion
        if (phase === 'initial' && currentQuestionIndex === 3) {
          // End of initial phase - calculate intermediate scores
          get().calculateIntermediateScores();
          await get().generateConfirmationQuestions();
        } else if (phase === 'confirmation' && currentQuestionIndex === 5) {
          // End of confirmation phase - determine primary temperament
          get().calculateIntermediateScores();

          const { intermediateScores } = get();
          const sortedTemperaments = Object.entries(intermediateScores)
            .sort(([, scoreA], [, scoreB]) => scoreB - scoreA)
            .map(([temp]) => temp as TemperamentType);

          const primaryTemp = sortedTemperaments[0];
          const compatibleTemps = TEMPERAMENT_COMPATIBILITY[primaryTemp];

          set({
            primaryTemperament: primaryTemp,
            compatibleTemperaments: compatibleTemps,
          });

          await get().generateComparisonQuestions();
        } else if (isLastQuestion(get())) {
          // End of assessment - calculate final results and submit
          get().calculateResults();
          if (get().result) {
            await get().submitAssessment();
          }
        }
      } catch (error) {
        handleError(error, { action: 'handleNextQuestion' });
      }
    },

    // Handle previous question
    handlePreviousQuestion: () => {
      const { currentQuestionIndex, responses } = get();

      if (currentQuestionIndex > 0) {
        // Remove the last response and go back
        const newResponses = responses.slice(0, -1);

        set({
          currentQuestionIndex: currentQuestionIndex - 1,
          responses: newResponses,
          selectedAnswer: null,
        });
      }
    },

    // Calculate intermediate scores
    calculateIntermediateScores: () => {
      const { responses } = get();

      const scores = { ...DEFAULT_SCORES };

      // Count scores for each temperament
      responses.forEach((temperamentIndex) => {
        const temperament = TEMPERAMENT_MAPPING[temperamentIndex];
        scores[temperament]++;
      });

      console.log('🧮 Intermediate scores:', scores);

      // Find the top temperaments
      const sortedTemperaments = Object.entries(scores)
        .sort(([, scoreA], [, scoreB]) => scoreB - scoreA)
        .map(([temp]) => temp as TemperamentType);

      console.log('🏆 Top temperaments:', sortedTemperaments);

      set({
        intermediateScores: scores,
        topTemperaments: sortedTemperaments.slice(0, 2),
      });
    },

    // Calculate final results
    calculateResults: () => {
      const { responses } = get();

      const scores = { ...DEFAULT_SCORES };

      // Count scores for each temperament
      responses.forEach((temperamentIndex) => {
        const temperament = TEMPERAMENT_MAPPING[temperamentIndex];
        scores[temperament]++;
      });

      const total = responses.length;
      const percentages = {
        choleric: Math.round((scores.choleric / total) * 100),
        sanguine: Math.round((scores.sanguine / total) * 100),
        melancholic: Math.round((scores.melancholic / total) * 100),
        phlegmatic: Math.round((scores.phlegmatic / total) * 100),
      };

      // Find primary and secondary temperaments
      const sortedEntries = Object.entries(percentages).sort(
        ([, a], [, b]) => b - a
      );

      const [primaryTemp, primaryPercentage] = sortedEntries[0];
      const [secondaryTemp, secondaryPercentage] = sortedEntries[1];

      const result = {
        primaryTemperament: primaryTemp as TemperamentType,
        secondaryTemperament: secondaryTemp as TemperamentType,
        primaryPercentage: primaryPercentage as number,
        secondaryPercentage: secondaryPercentage as number,
        temperamentScores: scores,
        completedAt: new Date().toISOString(),
      };

      console.log('🎯 Final assessment result:', result);

      set({
        result,
        ...createCompletionState(),
      });
    },

    // Submit assessment
    submitAssessment: async () => {
      try {
        const state = get();
        const { result, isDemo } = state;

        if (!result) {
          throw new Error('No assessment result to submit');
        }

        set(createSubmittingState());

        // Skip submission for demo mode
        if (isDemo) {
          console.log('🎮 Demo mode - skipping submission');
          set({ isSubmitting: false });
          return;
        }

        // Check authentication
        if (!isSupabaseConfigured()) {
          throw createAuthenticationError();
        }

        const {
          data: { user },
        } = await supabase.auth.getUser();
        if (!user) {
          throw createAuthenticationError();
        }

        // Prepare profile data
        const profileData = {
          user_id: user.id,
          dominant_temperament: result.primaryTemperament,
          secondary_temperament: result.secondaryTemperament,
          dominant_percentage: result.primaryPercentage,
          secondary_percentage: result.secondaryPercentage,
          assessment_completed_at: result.completedAt,
        };

        // Submit to database with retry
        await retryWithTimeout(async () => {
          const { error: insertError } = await supabase
            .from('personality_profiles')
            .insert(profileData);

          if (insertError) {
            throw insertError;
          }
        });

        console.log('✅ Assessment submitted successfully');
        set({ isSubmitting: false });

        // Navigate to results
        router.replace('/results');
      } catch (error) {
        handleError(createSubmissionError(error), {
          action: 'submitAssessment',
        });
        set({ isSubmitting: false });
      }
    },

    // Regenerate questions
    regenerateQuestions: async () => {
      await get().loadInitialQuestions();
    },

    // Use fallback questions
    useFallbackQuestions: () => {
      const fallbackQuestions = openRouterService.getFallbackQuestions();
      set({
        questions: fallbackQuestions.questions
          .slice(0, 4)
          .map((q: any, idx: number) => ({
            ...q,
            questionNumber: idx + 1,
            category: 'initial' as const,
          })),
        useAI: false,
        aiError: null,
        error: null,
        isLoading: false,
        isGeneratingQuestions: false,
      });
    },

    // Reset assessment
    resetAssessment: () => {
      set(createResetState());
    },

    // Clear error
    clearError: () => {
      set({ error: null, aiError: null });
    },

    // Get progress percentage
    getProgressPercentage: () => {
      return calculateProgress(get());
    },

    // Load demo assessment
    loadDemoAssessment: () => {
      console.log('🎮 Loading demo assessment...');

      const fallbackQuestions = openRouterService.getFallbackQuestions();

      set({
        ...createDemoState(),
        questions: fallbackQuestions.questions
          .slice(0, 4)
          .map((q: any, idx: number) => ({
            ...q,
            questionNumber: idx + 1,
            category: 'initial' as const,
          })),
      });
    },

    // Enhanced error handling actions
    setError: (error) => {
      set({ error });
    },

    retryAction: async (action) => {
      try {
        set({ error: null });
        await retryWithTimeout(action);
      } catch (error) {
        handleError(error, { action: 'retryAction' });
      }
    },
  };
};
