import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
} from 'react-native';
import { router } from 'expo-router';
import { supabase, isSupabaseConfigured } from '@/lib/supabase';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  ArrowLeft,
  User,
  Mail,
  Lock,
  Eye,
  EyeOff,
  Info,
  TriangleAlert as AlertTriangle,
} from 'lucide-react-native';

export default function RegisterScreen() {
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<{
    fullName?: string;
    email?: string;
    password?: string;
    confirmPassword?: string;
    general?: string;
  }>({});

  const clearError = (field?: string) => {
    if (field) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    } else {
      setErrors({});
    }
  };

  const validateField = (field: string, value: string) => {
    switch (field) {
      case 'fullName':
        if (!value.trim()) return 'Full name is required';
        if (value.trim().length < 2)
          return 'Full name must be at least 2 characters';
        break;
      case 'email':
        if (!value.trim()) return 'Email is required';
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value))
          return 'Please enter a valid email address';
        break;
      case 'password':
        if (!value) return 'Password is required';
        if (value.length < 6) return 'Password must be at least 6 characters';
        if (value.length > 128)
          return 'Password must be less than 128 characters';
        break;
      case 'confirmPassword':
        if (!value) return 'Please confirm your password';
        if (value !== password) return 'Passwords do not match';
        break;
    }
    return null;
  };

  const validateForm = () => {
    const newErrors: typeof errors = {};

    const fullNameError = validateField('fullName', fullName);
    if (fullNameError) newErrors.fullName = fullNameError;

    const emailError = validateField('email', email);
    if (emailError) newErrors.email = emailError;

    const passwordError = validateField('password', password);
    if (passwordError) newErrors.password = passwordError;

    const confirmPasswordError = validateField(
      'confirmPassword',
      confirmPassword
    );
    if (confirmPasswordError) newErrors.confirmPassword = confirmPasswordError;

    if (!isSupabaseConfigured()) {
      newErrors.general =
        'Registration is not available in demo mode. Please configure Supabase first.';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleRegister = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      // Register the user
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
          },
        },
      });

      if (error) throw error;

      if (data.user) {
        // Create email verification
        const { error: verificationError } = await supabase.rpc(
          'create_email_verification',
          {
            user_email: email,
          }
        );

        if (verificationError) {
          console.error(
            'Failed to create email verification:',
            verificationError
          );
        }

        // Navigate directly to verification screen
        router.push({
          pathname: '/auth/verify-email',
          params: { email },
        });
      }
    } catch (error: any) {
      setErrors({ general: error.message });
    } finally {
      setLoading(false);
    }
  };

  const isConfigured = isSupabaseConfigured();

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <ArrowLeft size={24} color="#1F2937" />
        </TouchableOpacity>
        <Text style={styles.title}>Sign Up</Text>
        <View style={{ width: 24 }} />
      </View>

      {!isConfigured && (
        <View style={styles.demoNotice}>
          <Info size={20} color="#F59E0B" />
          <View style={styles.demoNoticeContent}>
            <Text style={styles.demoNoticeTitle}>Demo Mode</Text>
            <Text style={styles.demoNoticeText}>
              Supabase is not configured. Registration won't work in demo mode.
            </Text>
          </View>
        </View>
      )}

      <View style={styles.form}>
        {errors.general && (
          <View style={styles.errorContainer}>
            <AlertTriangle size={20} color="#DC2626" />
            <View style={styles.errorContent}>
              <Text style={styles.errorText}>{errors.general}</Text>
            </View>
          </View>
        )}

        <View style={styles.inputContainer}>
          <User size={20} color="#6B7280" />
          <TextInput
            style={[styles.input, errors.fullName && styles.inputError]}
            placeholder="Full Name"
            value={fullName}
            onChangeText={(text) => {
              setFullName(text);
              clearError('fullName');
            }}
            onBlur={() => {
              const error = validateField('fullName', fullName);
              if (error) {
                setErrors((prev) => ({ ...prev, fullName: error }));
              }
            }}
          />
        </View>
        {errors.fullName && (
          <Text style={styles.fieldErrorText}>{errors.fullName}</Text>
        )}

        <View style={styles.inputContainer}>
          <Mail size={20} color="#6B7280" />
          <TextInput
            style={[styles.input, errors.email && styles.inputError]}
            placeholder="Email"
            value={email}
            onChangeText={(text) => {
              setEmail(text);
              clearError('email');
            }}
            onBlur={() => {
              const error = validateField('email', email);
              if (error) {
                setErrors((prev) => ({ ...prev, email: error }));
              }
            }}
            keyboardType="email-address"
            autoCapitalize="none"
          />
        </View>
        {errors.email && (
          <Text style={styles.fieldErrorText}>{errors.email}</Text>
        )}

        <View style={styles.inputContainer}>
          <Lock size={20} color="#6B7280" />
          <TextInput
            style={[styles.input, errors.password && styles.inputError]}
            placeholder="Password"
            value={password}
            onChangeText={(text) => {
              setPassword(text);
              clearError('password');
              // Also clear confirm password error if it was due to mismatch
              if (errors.confirmPassword && confirmPassword) {
                clearError('confirmPassword');
              }
            }}
            onBlur={() => {
              const error = validateField('password', password);
              if (error) {
                setErrors((prev) => ({ ...prev, password: error }));
              }
            }}
            secureTextEntry={!showPassword}
          />
          <TouchableOpacity onPress={() => setShowPassword(!showPassword)}>
            {showPassword ? (
              <EyeOff size={20} color="#6B7280" />
            ) : (
              <Eye size={20} color="#6B7280" />
            )}
          </TouchableOpacity>
        </View>
        {errors.password && (
          <Text style={styles.fieldErrorText}>{errors.password}</Text>
        )}

        <View style={styles.inputContainer}>
          <Lock size={20} color="#6B7280" />
          <TextInput
            style={[styles.input, errors.confirmPassword && styles.inputError]}
            placeholder="Confirm Password"
            value={confirmPassword}
            onChangeText={(text) => {
              setConfirmPassword(text);
              clearError('confirmPassword');
            }}
            onBlur={() => {
              const error = validateField('confirmPassword', confirmPassword);
              if (error) {
                setErrors((prev) => ({ ...prev, confirmPassword: error }));
              }
            }}
            secureTextEntry={!showConfirmPassword}
          />
          <TouchableOpacity
            onPress={() => setShowConfirmPassword(!showConfirmPassword)}
          >
            {showConfirmPassword ? (
              <EyeOff size={20} color="#6B7280" />
            ) : (
              <Eye size={20} color="#6B7280" />
            )}
          </TouchableOpacity>
        </View>
        {errors.confirmPassword && (
          <Text style={styles.fieldErrorText}>{errors.confirmPassword}</Text>
        )}

        <TouchableOpacity
          style={[
            styles.button,
            (loading || !isConfigured) && styles.buttonDisabled,
          ]}
          onPress={handleRegister}
          disabled={loading || !isConfigured}
        >
          <Text style={styles.buttonText}>
            {loading ? 'Creating Account...' : 'Create Account'}
          </Text>
        </TouchableOpacity>

        <View style={styles.footer}>
          <Text style={styles.footerText}>Already have an account? </Text>
          <TouchableOpacity onPress={() => router.push('/auth/login')}>
            <Text style={styles.footerLink}>Sign In</Text>
          </TouchableOpacity>
        </View>

        {!isConfigured && (
          <View style={styles.setupInstructions}>
            <Text style={styles.setupTitle}>To enable registration:</Text>
            <Text style={styles.setupText}>
              1. Create a Supabase project at supabase.com{'\n'}
              2. Copy your project URL and anon key{'\n'}
              3. Update the .env file with your credentials{'\n'}
              4. Run the database migration
            </Text>
          </View>
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    paddingVertical: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1F2937',
  },
  demoNotice: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#FEF3C7',
    marginHorizontal: 24,
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
    gap: 12,
  },
  demoNoticeContent: {
    flex: 1,
  },
  demoNoticeTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#92400E',
    marginBottom: 4,
  },
  demoNoticeText: {
    fontSize: 14,
    color: '#92400E',
    lineHeight: 20,
  },
  form: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 32,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#FEF2F2',
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
    gap: 12,
  },
  errorContent: {
    flex: 1,
  },
  errorText: {
    fontSize: 14,
    color: '#DC2626',
    lineHeight: 20,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    marginBottom: 16,
    gap: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: '#1F2937',
  },
  inputError: {
    borderColor: '#DC2626',
    borderWidth: 1,
  },
  fieldErrorText: {
    fontSize: 12,
    color: '#DC2626',
    marginTop: -12,
    marginBottom: 16,
    marginLeft: 16,
  },
  button: {
    backgroundColor: '#3B82F6',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 32,
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 24,
  },
  footerText: {
    fontSize: 14,
    color: '#6B7280',
  },
  footerLink: {
    fontSize: 14,
    color: '#3B82F6',
    fontWeight: '500',
  },
  setupInstructions: {
    backgroundColor: '#F0F9FF',
    padding: 16,
    borderRadius: 12,
    marginTop: 16,
  },
  setupTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1E40AF',
    marginBottom: 8,
  },
  setupText: {
    fontSize: 14,
    color: '#1E40AF',
    lineHeight: 20,
  },
});
