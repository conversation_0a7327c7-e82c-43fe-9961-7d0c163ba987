import { TemperamentType } from '@/types/personality';
import { AssessmentConfig, RetryConfig } from './types';

// Temperament mapping from index to temperament type
export const TEMPERAMENT_MAPPING: Record<number, TemperamentType> = {
  0: 'choleric',
  1: 'sanguine', 
  2: 'melancholic',
  3: 'phlegmatic',
} as const;

// Temperament compatibility mapping
export const TEMPERAMENT_COMPATIBILITY: Record<TemperamentType, TemperamentType[]> = {
  choleric: ['sanguine', 'melancholic'],
  sanguine: ['choleric', 'phlegmatic'],
  melancholic: ['choleric', 'phlegmatic'],
  phlegmatic: ['sanguine', 'melancholic'],
} as const;

// Assessment configuration
export const ASSESSMENT_CONFIG: AssessmentConfig = {
  questionsPerPhase: {
    initial: 4,
    confirmation: 2,
    comparison: 3,
  },
  maxRetries: 3,
  retryDelayMs: 1000,
  aiTimeoutMs: 30000,
  progressSaveIntervalMs: 5000,
} as const;

// Retry configuration for error handling
export const RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  baseDelayMs: 1000,
  maxDelayMs: 10000,
  backoffMultiplier: 2,
} as const;

// Loading states
export const LOADING_STATES = {
  IDLE: 'idle',
  LOADING: 'loading',
  GENERATING: 'generating',
  SUBMITTING: 'submitting',
} as const;

// Error types
export const ERROR_TYPES = {
  NETWORK: 'network',
  VALIDATION: 'validation',
  AI_GENERATION: 'ai_generation',
  SUBMISSION: 'submission',
  AUTHENTICATION: 'authentication',
} as const;

// Assessment phases
export const ASSESSMENT_PHASES = {
  INITIAL: 'initial',
  CONFIRMATION: 'confirmation',
  COMPARISON: 'comparison',
  COMPLETE: 'complete',
} as const;

// Question categories
export const QUESTION_CATEGORIES = {
  INITIAL: 'initial',
  CONFIRMATION: 'confirmation',
  COMPARISON: 'comparison',
  TIEBREAKER: 'tiebreaker',
} as const;

// Question generation settings
export const QUESTION_GENERATION = {
  TEMPERATURE: 0.8,
  MAX_TOKENS: 6000,
  TOP_P: 1,
  MODEL: 'google/gemini-2.5-flash-preview-05-20',
} as const;

// Progress calculation constants
export const PROGRESS_WEIGHTS = {
  INITIAL_PHASE: 0.4,      // 40% for initial questions
  CONFIRMATION_PHASE: 0.2,  // 20% for confirmation
  COMPARISON_PHASE: 0.3,    // 30% for comparison
  COMPLETION: 0.1,          // 10% for final submission
} as const;

// Scoring weights by phase (for enhanced scoring algorithm)
export const PHASE_WEIGHTS = {
  initial: 1.0,
  confirmation: 1.5,    // Confirmation questions are more important
  comparison: 2.0,      // Comparison questions are most important
  tiebreaker: 2.5,      // Tiebreaker questions have highest weight
} as const;

// Error messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network connection failed. Please check your internet connection.',
  AI_GENERATION_FAILED: 'Failed to generate AI questions. Using standard questions instead.',
  SUBMISSION_FAILED: 'Failed to submit assessment. Please try again.',
  AUTHENTICATION_REQUIRED: 'Please sign in to save your assessment results.',
  VALIDATION_ERROR: 'Invalid data provided. Please check your responses.',
  GENERIC_ERROR: 'An unexpected error occurred. Please try again.',
  TIMEOUT_ERROR: 'Request timed out. Please try again.',
} as const;

// Success messages
export const SUCCESS_MESSAGES = {
  ASSESSMENT_SUBMITTED: 'Assessment completed successfully!',
  QUESTIONS_GENERATED: 'Fresh questions generated successfully.',
  PROFILE_UPDATED: 'Your personality profile has been updated.',
} as const;

// API endpoints and configuration
export const API_CONFIG = {
  OPENROUTER_URL: 'https://openrouter.ai/api/v1/chat/completions',
  REQUEST_TIMEOUT: 30000,
  MAX_RETRIES: 3,
} as const;

// Storage keys for persistence
export const STORAGE_KEYS = {
  ASSESSMENT_STATE: 'assessment-storage',
  USER_PREFERENCES: 'user-preferences',
  DEMO_MODE: 'demo-mode',
} as const;

// Validation rules
export const VALIDATION_RULES = {
  MIN_QUESTIONS_PER_PHASE: 1,
  MAX_QUESTIONS_PER_PHASE: 10,
  MIN_ANSWERS_PER_QUESTION: 2,
  MAX_ANSWERS_PER_QUESTION: 4,
  MIN_QUESTION_LENGTH: 10,
  MAX_QUESTION_LENGTH: 500,
  MIN_ANSWER_LENGTH: 5,
  MAX_ANSWER_LENGTH: 200,
} as const;

// Default temperament scores
export const DEFAULT_SCORES = {
  choleric: 0,
  sanguine: 0,
  melancholic: 0,
  phlegmatic: 0,
} as const;

// Temperament display names
export const TEMPERAMENT_NAMES: Record<TemperamentType, string> = {
  choleric: 'Choleric',
  sanguine: 'Sanguine',
  melancholic: 'Melancholic',
  phlegmatic: 'Phlegmatic',
} as const;

// Temperament descriptions (short)
export const TEMPERAMENT_DESCRIPTIONS: Record<TemperamentType, string> = {
  choleric: 'Goal-oriented and decisive',
  sanguine: 'Enthusiastic and social',
  melancholic: 'Thoughtful and analytical',
  phlegmatic: 'Peaceful and steady',
} as const;

// Assessment completion thresholds
export const COMPLETION_THRESHOLDS = {
  MIN_RESPONSES: 6,        // Minimum responses to calculate results
  CONFIDENCE_THRESHOLD: 0.6, // Minimum confidence for primary temperament
  TIE_THRESHOLD: 0.1,      // Threshold for considering scores tied
} as const;
