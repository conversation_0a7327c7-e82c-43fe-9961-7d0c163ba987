import { AssessmentState, TemperamentScores } from './types';
import { TemperamentType } from '../../types/personality';
import {
  getCurrentQuestion,
  canNavigateBack,
  canProceed,
  isFirstQuestion,
  isLastQuestion,
  calculateProgress,
  isAssessmentComplete,
  isDemoMode,
  isUsingAI,
  hasError,
  isLoading,
  getTotalQuestions,
  getCompletedQuestions,
} from './state';
import { TEMPERAMENT_MAPPING, PHASE_WEIGHTS } from './constants';

// Basic selectors (re-exported from state for consistency)
export {
  getCurrentQuestion,
  canNavigateBack,
  canProceed,
  isFirstQuestion,
  isLastQuestion,
  calculateProgress,
  isAssessmentComplete,
  isDemoMode,
  isUsingAI,
  hasError,
  isLoading,
  getTotalQuestions,
  getCompletedQuestions,
};

// Advanced selectors for derived state

// Get current question with safety checks
export const selectCurrentQuestion = (state: AssessmentState) => {
  return getCurrentQuestion(state);
};

// Get navigation state
export const selectNavigationState = (state: AssessmentState) => ({
  canGoBack: canNavigateBack(state),
  canProceed: canProceed(state),
  isFirst: isFirstQuestion(state),
  isLast: isLastQuestion(state),
  currentIndex: state.currentQuestionIndex,
  totalQuestions: state.questions.length,
});

// Get loading state
export const selectLoadingState = (state: AssessmentState) => ({
  isLoading: state.isLoading,
  isGenerating: state.isGeneratingQuestions,
  isSubmitting: state.isSubmitting,
  anyLoading: isLoading(state),
});

// Get error state
export const selectErrorState = (state: AssessmentState) => ({
  hasError: hasError(state),
  error: state.error,
  aiError: state.aiError,
  isRetryable: state.error?.retryable ?? false,
});

// Get assessment configuration
export const selectAssessmentConfig = (state: AssessmentState) => ({
  isRetake: state.isRetake,
  useAI: state.useAI,
  isDemo: state.isDemo,
  config: state.config,
});

// Get progress information
export const selectProgressInfo = (state: AssessmentState) => ({
  percentage: calculateProgress(state),
  completed: getCompletedQuestions(state),
  total: getTotalQuestions(state),
  phase: state.phase,
  currentQuestion: state.currentQuestionIndex + 1,
  totalQuestions: state.questions.length,
});

// Get scoring information
export const selectScoringInfo = (state: AssessmentState) => ({
  intermediateScores: state.intermediateScores,
  topTemperaments: state.topTemperaments,
  primaryTemperament: state.primaryTemperament,
  compatibleTemperaments: state.compatibleTemperaments,
  result: state.result,
});

// Calculate weighted scores (enhanced scoring algorithm)
export const selectWeightedScores = (
  state: AssessmentState
): TemperamentScores => {
  const weightedScores = {
    choleric: 0,
    sanguine: 0,
    melancholic: 0,
    phlegmatic: 0,
  };

  // Apply weights based on question phase and category
  state.responses.forEach((temperamentIndex, responseIndex) => {
    const question = state.questions[responseIndex];
    if (!question) return;

    const temperament = TEMPERAMENT_MAPPING[temperamentIndex];
    const weight = PHASE_WEIGHTS[question.category] || 1.0;

    weightedScores[temperament] += weight;
  });

  return weightedScores;
};

// Get sorted temperaments by score
export const selectSortedTemperaments = (
  state: AssessmentState
): Array<{
  temperament: TemperamentType;
  score: number;
  percentage: number;
}> => {
  const scores = selectWeightedScores(state);
  const totalScore = Object.values(scores).reduce(
    (sum, score) => sum + score,
    0
  );

  return Object.entries(scores)
    .map(([temperament, score]) => ({
      temperament: temperament as TemperamentType,
      score,
      percentage: totalScore > 0 ? Math.round((score / totalScore) * 100) : 0,
    }))
    .sort((a, b) => b.score - a.score);
};

// Get confidence level for primary temperament
export const selectConfidenceLevel = (state: AssessmentState): number => {
  const sortedTemperaments = selectSortedTemperaments(state);

  if (sortedTemperaments.length < 2) return 0;

  const [primary, secondary] = sortedTemperaments;
  const difference = primary.percentage - secondary.percentage;

  // Convert percentage difference to confidence (0-1)
  return Math.min(difference / 50, 1); // 50% difference = 100% confidence
};

// Check if there's a tie in scores
export const selectHasTie = (state: AssessmentState): boolean => {
  const sortedTemperaments = selectSortedTemperaments(state);

  if (sortedTemperaments.length < 2) return false;

  const [primary, secondary] = sortedTemperaments;
  return Math.abs(primary.score - secondary.score) <= 1; // Tie if difference is 1 or less
};

// Get recommended next action
export const selectRecommendedAction = (state: AssessmentState): string => {
  if (hasError(state)) return 'retry';
  if (isLoading(state)) return 'wait';
  if (!canProceed(state)) return 'select_answer';
  if (isLastQuestion(state)) return 'complete';
  return 'next_question';
};

// Get assessment summary
export const selectAssessmentSummary = (state: AssessmentState) => {
  const progressInfo = selectProgressInfo(state);
  const scoringInfo = selectScoringInfo(state);
  const sortedTemperaments = selectSortedTemperaments(state);
  const confidence = selectConfidenceLevel(state);
  const hasTie = selectHasTie(state);

  return {
    progress: progressInfo,
    scoring: scoringInfo,
    temperaments: sortedTemperaments,
    confidence,
    hasTie,
    isComplete: isAssessmentComplete(state),
    phase: state.phase,
  };
};

// Get question statistics
export const selectQuestionStats = (state: AssessmentState) => {
  const questionsByCategory = state.questions.reduce((acc, question) => {
    acc[question.category] = (acc[question.category] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const responsesByTemperament = state.responses.reduce(
    (acc, temperamentIndex) => {
      const temperament = TEMPERAMENT_MAPPING[temperamentIndex];
      acc[temperament] = (acc[temperament] || 0) + 1;
      return acc;
    },
    {} as Record<TemperamentType, number>
  );

  return {
    totalQuestions: state.questions.length,
    questionsByCategory,
    totalResponses: state.responses.length,
    responsesByTemperament,
    completionRate:
      state.questions.length > 0
        ? (state.responses.length / state.questions.length) * 100
        : 0,
  };
};

// Get phase-specific information
export const selectPhaseInfo = (state: AssessmentState) => {
  const phaseQuestions = {
    initial: state.config.questionsPerPhase.initial,
    confirmation: state.config.questionsPerPhase.confirmation,
    comparison: state.config.questionsPerPhase.comparison,
  };

  const currentPhaseProgress = (() => {
    const completed = state.responses.length;

    switch (state.phase) {
      case 'initial':
        return Math.min(completed, phaseQuestions.initial);
      case 'confirmation':
        return Math.max(0, completed - phaseQuestions.initial);
      case 'comparison':
        return Math.max(
          0,
          completed - phaseQuestions.initial - phaseQuestions.confirmation
        );
      default:
        return 0;
    }
  })();

  const currentPhaseTotal = phaseQuestions[state.phase] || 0;

  return {
    phase: state.phase,
    phaseQuestions,
    currentPhaseProgress,
    currentPhaseTotal,
    phasePercentage:
      currentPhaseTotal > 0
        ? (currentPhaseProgress / currentPhaseTotal) * 100
        : 0,
  };
};
