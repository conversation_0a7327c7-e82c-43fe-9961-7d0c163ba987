/**
 * Basic tests for the refactored assessment store
 * This ensures the modular store works correctly
 */

import { useAssessmentStore } from '../index';
import { 
  createAssessmentError, 
  createNetworkError,
  validateAssessmentQuestion,
  selectCurrentQuestion,
  selectNavigationState,
  calculateProgress,
} from '../index';

// Mock external dependencies
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
}));

describe('Assessment Store Refactoring', () => {
  beforeEach(() => {
    // Reset store before each test
    useAssessmentStore.getState().resetAssessment();
  });

  describe('Store Structure', () => {
    it('should have all required state properties', () => {
      const state = useAssessmentStore.getState();
      
      // Core state
      expect(state.phase).toBeDefined();
      expect(state.questions).toBeDefined();
      expect(state.currentQuestionIndex).toBeDefined();
      expect(state.responses).toBeDefined();
      expect(state.selectedAnswer).toBeDefined();
      
      // Scoring state
      expect(state.intermediateScores).toBeDefined();
      expect(state.topTemperaments).toBeDefined();
      expect(state.primaryTemperament).toBeDefined();
      expect(state.compatibleTemperaments).toBeDefined();
      expect(state.result).toBeDefined();
      
      // Loading states
      expect(state.isLoading).toBeDefined();
      expect(state.isGeneratingQuestions).toBeDefined();
      expect(state.isSubmitting).toBeDefined();
      
      // Error handling
      expect(state.error).toBeDefined();
      expect(state.aiError).toBeDefined();
      
      // Configuration
      expect(state.isRetake).toBeDefined();
      expect(state.useAI).toBeDefined();
      expect(state.isDemo).toBeDefined();
      expect(state.config).toBeDefined();
    });

    it('should have all required action methods', () => {
      const state = useAssessmentStore.getState();
      
      // Initialization
      expect(typeof state.initializeAssessment).toBe('function');
      expect(typeof state.loadInitialQuestions).toBe('function');
      expect(typeof state.loadDemoAssessment).toBe('function');
      
      // Question generation
      expect(typeof state.generateConfirmationQuestions).toBe('function');
      expect(typeof state.generateComparisonQuestions).toBe('function');
      expect(typeof state.regenerateQuestions).toBe('function');
      expect(typeof state.useFallbackQuestions).toBe('function');
      
      // Navigation and interaction
      expect(typeof state.handleAnswerSelect).toBe('function');
      expect(typeof state.handleNextQuestion).toBe('function');
      expect(typeof state.handlePreviousQuestion).toBe('function');
      
      // Scoring and results
      expect(typeof state.calculateIntermediateScores).toBe('function');
      expect(typeof state.calculateResults).toBe('function');
      expect(typeof state.submitAssessment).toBe('function');
      
      // Utility actions
      expect(typeof state.resetAssessment).toBe('function');
      expect(typeof state.clearError).toBe('function');
      expect(typeof state.getProgressPercentage).toBe('function');
      
      // Error handling actions
      expect(typeof state.setError).toBe('function');
      expect(typeof state.retryAction).toBe('function');
    });
  });

  describe('Error Handling', () => {
    it('should create structured errors correctly', () => {
      const networkError = createNetworkError(new Error('Connection failed'));
      
      expect(networkError.type).toBe('network');
      expect(networkError.message).toBe('Network connection failed. Please check your internet connection.');
      expect(networkError.retryable).toBe(true);
      expect(networkError.context).toBeDefined();
      expect(networkError.timestamp).toBeDefined();
    });

    it('should create generic assessment errors', () => {
      const error = createAssessmentError('validation', 'Invalid input', false, { field: 'test' });
      
      expect(error.type).toBe('validation');
      expect(error.message).toBe('Invalid input');
      expect(error.retryable).toBe(false);
      expect(error.context).toEqual({ field: 'test' });
      expect(error.timestamp).toBeDefined();
    });

    it('should handle error state in store', () => {
      const store = useAssessmentStore.getState();
      const testError = createAssessmentError('network', 'Test error');
      
      store.setError(testError);
      
      const state = useAssessmentStore.getState();
      expect(state.error).toEqual(testError);
      
      store.clearError();
      
      const clearedState = useAssessmentStore.getState();
      expect(clearedState.error).toBeNull();
    });
  });

  describe('Validation', () => {
    it('should validate assessment questions correctly', () => {
      const validQuestion = {
        questionNumber: 1,
        category: 'initial' as const,
        question: 'Test question with sufficient length',
        answers: ['Answer A', 'Answer B', 'Answer C', 'Answer D'],
        temperamentOrder: [0, 1, 2, 3],
      };
      
      expect(validateAssessmentQuestion(validQuestion)).toBe(true);
    });

    it('should reject invalid assessment questions', () => {
      const invalidQuestion = {
        questionNumber: 1,
        category: 'invalid' as any,
        question: 'Short',
        answers: ['A'],
        temperamentOrder: [0],
      };
      
      expect(validateAssessmentQuestion(invalidQuestion)).toBe(false);
    });
  });

  describe('Selectors', () => {
    it('should calculate progress correctly', () => {
      const state = useAssessmentStore.getState();
      
      // Initial state should have 0% progress
      expect(calculateProgress(state)).toBe(0);
    });

    it('should get current question safely', () => {
      const state = useAssessmentStore.getState();
      
      // No questions loaded initially
      expect(selectCurrentQuestion(state)).toBeNull();
    });

    it('should get navigation state correctly', () => {
      const state = useAssessmentStore.getState();
      const navState = selectNavigationState(state);
      
      expect(navState.canGoBack).toBe(false);
      expect(navState.canProceed).toBe(false);
      expect(navState.isFirst).toBe(true);
      expect(navState.isLast).toBe(false);
      expect(navState.currentIndex).toBe(0);
      expect(navState.totalQuestions).toBe(0);
    });
  });

  describe('State Management', () => {
    it('should handle answer selection', () => {
      const store = useAssessmentStore.getState();
      
      store.handleAnswerSelect(2);
      
      const state = useAssessmentStore.getState();
      expect(state.selectedAnswer).toBe(2);
    });

    it('should reset assessment correctly', () => {
      const store = useAssessmentStore.getState();
      
      // Make some changes
      store.handleAnswerSelect(1);
      store.setError(createAssessmentError('network', 'Test error'));
      
      // Reset
      store.resetAssessment();
      
      const state = useAssessmentStore.getState();
      expect(state.selectedAnswer).toBeNull();
      expect(state.error).toBeNull();
      expect(state.currentQuestionIndex).toBe(0);
      expect(state.responses).toEqual([]);
    });

    it('should handle demo mode correctly', () => {
      const store = useAssessmentStore.getState();
      
      store.loadDemoAssessment();
      
      const state = useAssessmentStore.getState();
      expect(state.isDemo).toBe(true);
      expect(state.useAI).toBe(false);
      expect(state.isLoading).toBe(false);
    });
  });

  describe('Configuration', () => {
    it('should have proper default configuration', () => {
      const state = useAssessmentStore.getState();
      
      expect(state.config.questionsPerPhase.initial).toBe(4);
      expect(state.config.questionsPerPhase.confirmation).toBe(2);
      expect(state.config.questionsPerPhase.comparison).toBe(3);
      expect(state.config.maxRetries).toBe(3);
      expect(state.config.retryDelayMs).toBe(1000);
      expect(state.config.aiTimeoutMs).toBe(30000);
      expect(state.config.progressSaveIntervalMs).toBe(5000);
    });
  });
});

describe('Development Utilities', () => {
  it('should provide dev utils in development mode', () => {
    // This would be available in __DEV__ mode
    const devUtils = {
      getState: useAssessmentStore.getState,
      subscribe: useAssessmentStore.subscribe,
      reset: () => useAssessmentStore.getState().resetAssessment(),
      forceError: (error: string) => 
        useAssessmentStore.getState().setError(createAssessmentError('network', error)),
      getStats: () => {
        const state = useAssessmentStore.getState();
        return {
          questionsLoaded: state.questions.length,
          responsesGiven: state.responses.length,
          currentPhase: state.phase,
          progressPercentage: calculateProgress(state),
          hasErrors: state.error !== null || state.aiError !== null,
          isDemo: state.isDemo,
          useAI: state.useAI,
        };
      },
    };
    
    expect(typeof devUtils.getState).toBe('function');
    expect(typeof devUtils.subscribe).toBe('function');
    expect(typeof devUtils.reset).toBe('function');
    expect(typeof devUtils.forceError).toBe('function');
    expect(typeof devUtils.getStats).toBe('function');
    
    const stats = devUtils.getStats();
    expect(stats).toHaveProperty('questionsLoaded');
    expect(stats).toHaveProperty('responsesGiven');
    expect(stats).toHaveProperty('currentPhase');
    expect(stats).toHaveProperty('progressPercentage');
    expect(stats).toHaveProperty('hasErrors');
    expect(stats).toHaveProperty('isDemo');
    expect(stats).toHaveProperty('useAI');
  });
});
