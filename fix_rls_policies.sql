-- Fix RLS Policies for personality_profiles table
-- Run this after the database cleanup script

-- Step 1: Check current RLS policies
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename = 'personality_profiles';

-- Step 2: Check if R<PERSON> is enabled
SELECT 
    schemaname,
    tablename,
    rowsecurity
FROM pg_tables 
WHERE tablename = 'personality_profiles';

-- Step 3: Drop existing problematic policies and recreate them properly
DROP POLICY IF EXISTS "Users can view own personality profiles" ON personality_profiles;
DROP POLICY IF EXISTS "Users can insert own personality profiles" ON personality_profiles;
DROP POLICY IF EXISTS "Users can update own personality profiles" ON personality_profiles;
DROP POLICY IF EXISTS "Users can delete own personality profiles" ON personality_profiles;

-- Step 4: Create comprehensive RLS policies that work with UPSERT

-- Policy for SELECT (viewing own profiles)
CREATE POLICY "Users can view own personality profiles" ON personality_profiles
    FOR SELECT USING (auth.uid() = user_id);

-- Policy for INSERT (creating new profiles)
CREATE POLICY "Users can insert own personality profiles" ON personality_profiles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Policy for UPDATE (updating existing profiles - needed for UPSERT)
CREATE POLICY "Users can update own personality profiles" ON personality_profiles
    FOR UPDATE USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

-- Policy for DELETE (deleting own profiles - needed for cleanup)
CREATE POLICY "Users can delete own personality profiles" ON personality_profiles
    FOR DELETE USING (auth.uid() = user_id);

-- Step 5: Ensure RLS is enabled
ALTER TABLE personality_profiles ENABLE ROW LEVEL SECURITY;

-- Step 6: Grant necessary permissions to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON personality_profiles TO authenticated;

-- Step 7: Verify the policies are working
SELECT 
    policyname,
    cmd,
    permissive,
    qual,
    with_check
FROM pg_policies 
WHERE tablename = 'personality_profiles'
ORDER BY cmd;

-- Step 8: Test policy with a simple query (this should work for authenticated users)
-- This will show the current user's profiles
SELECT 
    id,
    user_id,
    dominant_temperament,
    created_at
FROM personality_profiles 
WHERE user_id = auth.uid();

-- Success message
DO $$
BEGIN
    RAISE NOTICE 'RLS policies have been updated successfully!';
    RAISE NOTICE 'Users can now INSERT, UPDATE, SELECT, and DELETE their own personality profiles';
    RAISE NOTICE 'UPSERT operations should now work properly';
END $$;
